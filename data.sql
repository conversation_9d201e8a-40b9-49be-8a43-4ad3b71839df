--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: tenants; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.tenants (id, name, external_id, jwt_secret, max_concurrent_users, inserted_at, updated_at, max_events_per_second, postgres_cdc_default, max_bytes_per_second, max_channels_per_client, max_joins_per_second, suspend, jwt_jwks, notify_private_alpha, private_only) FROM stdin;
c3e7b940-784a-4e72-b2af-e681b16465cc	realtime-dev	realtime-dev	E5F8LBU3wKksAM30cEkxapvgr554NbBkz2mAGmK0d/K9as3HKsV/mEgRknprWVgbn/UBK23mf7uVJjXn3etQCU82uuGZrojyd3by3+a2jj8=	200	2025-08-03 02:36:48	2025-08-03 02:36:48	100	postgres_cdc_rls	100000	100	100	f	\N	f	f
\.


--
-- Data for Name: extensions; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.extensions (id, type, settings, tenant_external_id, inserted_at, updated_at) FROM stdin;
60bb4df9-d3da-4cee-995d-5f586e73b443	postgres_cdc_rls	{"region": "us-east-1", "db_host": "QhixI0o7PYIABziLUL4f0A==", "db_name": "sWBpZNdjggEPTQVlI52Zfw==", "db_port": "+enMDFi1J/3IrrquHHwUmA==", "db_user": "uxbEq/zz8DXVD53TOI1zmw==", "slot_name": "supabase_realtime_replication_slot", "db_password": "4nXDsx9OLqOJFwwLgYkF1E82uuGZrojyd3by3+a2jj8=", "publication": "supabase_realtime", "ssl_enforced": false, "poll_interval_ms": 100, "poll_max_changes": 100, "poll_max_record_bytes": 1048576}	realtime-dev	2025-08-03 02:36:48	2025-08-03 02:36:48
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: _realtime; Owner: supabase_admin
--

COPY _realtime.schema_migrations (version, inserted_at) FROM stdin;
20210706140551	2025-06-10 09:45:42
20220329161857	2025-06-10 09:45:42
20220410212326	2025-06-10 09:45:42
20220506102948	2025-06-10 09:45:42
20220527210857	2025-06-10 09:45:42
20220815211129	2025-06-10 09:45:42
20220815215024	2025-06-10 09:45:43
20220818141501	2025-06-10 09:45:43
20221018173709	2025-06-10 09:45:43
20221102172703	2025-06-10 09:45:43
20221223010058	2025-06-10 09:45:43
20230110180046	2025-06-10 09:45:43
20230810220907	2025-06-10 09:45:43
20230810220924	2025-06-10 09:45:43
20231024094642	2025-06-10 09:45:43
20240306114423	2025-06-10 09:45:43
20240418082835	2025-06-10 09:45:43
20240625211759	2025-06-10 09:45:43
20240704172020	2025-06-10 09:45:43
20240902173232	2025-06-10 09:45:43
20241106103258	2025-06-10 09:45:43
\.


--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.audit_log_entries (instance_id, id, payload, created_at, ip_address) FROM stdin;
********-0000-0000-0000-************	342bcfcd-0a57-4094-9747-0c56e0160665	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"000643ba-c3bb-4e09-b5ce-dc184e3a1880","user_phone":""}}	2025-06-10 10:01:29.625077+00	
********-0000-0000-0000-************	7e6ffdad-7078-4710-98b9-c954d70d4344	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"000643ba-c3bb-4e09-b5ce-dc184e3a1880","user_phone":""}}	2025-06-10 10:01:33.056559+00	
********-0000-0000-0000-************	4e302c74-b85a-476f-85b1-22a10b128779	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","user_phone":""}}	2025-06-10 10:01:41.959846+00	
********-0000-0000-0000-************	c2080fb6-8c26-4545-b1cd-dc61f475cc0f	{"action":"login","actor_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-10 10:14:03.124137+00	
********-0000-0000-0000-************	b8742cb4-773f-487b-9e5e-b63f5c5436c5	{"action":"logout","actor_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-10 10:14:09.848898+00	
********-0000-0000-0000-************	6880b81e-55e0-4c81-a3ea-a3792035c4ab	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"c8459552-3d20-4fdd-9b1f-b1023b5fc103","user_phone":""}}	2025-06-10 10:14:36.399431+00	
********-0000-0000-0000-************	94ead822-89bc-4126-8bed-b36992b723b8	{"action":"user_confirmation_requested","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-10 10:14:47.419956+00	
********-0000-0000-0000-************	4e8b00da-e1d3-47e8-8815-cdc7751b17c8	{"action":"user_signedup","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-10 10:15:02.073023+00	
********-0000-0000-0000-************	ff2af899-1df9-493b-8a09-b3d86b1dc2d6	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 11:14:32.998085+00	
********-0000-0000-0000-************	01ae40d5-eeb8-40e8-a363-557478bfc435	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 11:14:33.000857+00	
********-0000-0000-0000-************	d7fb7168-a5a2-4de5-aaa6-8ad2fad122c5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 13:41:18.156036+00	
********-0000-0000-0000-************	2f37d409-7b2e-495e-a6fa-7645c8966a36	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 13:41:18.158694+00	
********-0000-0000-0000-************	c83f11cb-4d1c-4d73-8570-984f321a490a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 14:52:04.817325+00	
********-0000-0000-0000-************	30068a03-e6e7-4ee8-828a-589314640879	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-10 14:52:04.819745+00	
********-0000-0000-0000-************	674cc3c9-e255-41aa-b240-fe479643c28c	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 10:42:38.834325+00	
********-0000-0000-0000-************	bb059115-e985-44ad-8570-329adf05950e	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 10:42:38.843642+00	
********-0000-0000-0000-************	df499976-2eba-4055-92ea-347401109227	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 20:48:52.179526+00	
********-0000-0000-0000-************	95280989-a6e6-4ce1-8a1f-24b7b4c1d5cf	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-11 20:48:52.189359+00	
********-0000-0000-0000-************	a7903538-1d17-4dd7-b0ae-f55c2a56ef22	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 09:37:54.588569+00	
********-0000-0000-0000-************	74288d68-f6e1-4c73-bcca-12f8c2db1100	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 09:37:54.595633+00	
********-0000-0000-0000-************	8d50fcb5-aa02-472a-9cef-db62964fbe84	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 10:38:51.53741+00	
********-0000-0000-0000-************	3fbee4d6-5be9-400d-afe4-f462befdc97d	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-12 10:38:51.540822+00	
********-0000-0000-0000-************	223eee71-6a3c-4a1e-8d72-68e3a27aa6d6	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-13 00:43:05.16218+00	
********-0000-0000-0000-************	4f5789d3-e87a-46a3-8cfb-7801eae1d24a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 00:45:02.95355+00	
********-0000-0000-0000-************	6a674572-6dda-4679-a9c0-7a5ee06ad252	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 00:45:02.954445+00	
********-0000-0000-0000-************	effcc998-bd78-4cda-9643-b7d8873f1625	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 10:24:28.00041+00	
********-0000-0000-0000-************	3bdd72c5-de44-41ca-83b5-61a1f022b31c	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-13 10:24:28.013198+00	
********-0000-0000-0000-************	76321bf6-1a96-4800-b536-9a46476282a5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 06:41:24.200275+00	
********-0000-0000-0000-************	61180987-4471-4353-996b-ddd783ab599d	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 06:41:24.213004+00	
********-0000-0000-0000-************	52235a2f-0935-4756-8701-8716039686a9	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 08:20:49.664563+00	
********-0000-0000-0000-************	697cea6b-04e6-4581-833c-be5a810a55e4	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 08:20:49.665419+00	
********-0000-0000-0000-************	e682bbe3-1bd3-422e-acd2-4b70716aa175	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 10:08:49.521167+00	
********-0000-0000-0000-************	8a00b8b6-9e7e-4eb3-93c4-9beabaf4046a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 10:08:49.528447+00	
********-0000-0000-0000-************	d2e9b635-c015-4c72-9a28-3709b503e296	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 11:36:12.065207+00	
********-0000-0000-0000-************	46d4089e-1afc-4ded-be15-3e01bb7dce4f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-14 11:36:12.085093+00	
********-0000-0000-0000-************	6f47e59e-8201-4b28-827c-3e871cd13885	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-14 14:33:01.380271+00	
********-0000-0000-0000-************	354b5d99-16fd-4ff4-a4a2-1ca717a9deb7	{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","user_phone":""}}	2025-06-14 14:34:12.332261+00	
********-0000-0000-0000-************	237931b9-072d-4776-84a7-0da307edd4ec	{"action":"logout","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-14 14:34:18.185316+00	
********-0000-0000-0000-************	4a1327b6-28a6-4214-89f9-66d4fd505141	{"action":"login","actor_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-14 14:34:20.660126+00	
********-0000-0000-0000-************	3ee80c11-87ee-4b41-ac8c-c599b68ae36f	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"b4a0703c-35b3-424e-aaea-ad886058e7c6","user_phone":""}}	2025-06-14 15:31:13.172608+00	
********-0000-0000-0000-************	938744cb-5716-4e98-b736-eedf7975cefb	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 00:30:18.338752+00	
********-0000-0000-0000-************	1f7aca1c-e597-423a-b7af-75283e3c360a	{"action":"logout","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-15 00:53:48.760914+00	
********-0000-0000-0000-************	902344ec-e084-4a68-88ca-32ab197c7680	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 00:53:54.358553+00	
********-0000-0000-0000-************	d5a627e7-d088-46e5-af63-b21a7ce60e2b	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 01:52:05.495286+00	
********-0000-0000-0000-************	41ad52d6-5643-4745-8d65-99d4002b901f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 01:52:05.496557+00	
********-0000-0000-0000-************	c69d4191-eef4-4250-b337-f3caaa0c7752	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 05:41:43.23739+00	
********-0000-0000-0000-************	d81b8fb3-6ff1-4f71-bf34-31e4a851f60a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 05:41:43.238383+00	
********-0000-0000-0000-************	ecc49315-0457-488b-916d-1371767d0904	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 07:53:36.008034+00	
********-0000-0000-0000-************	0bce154f-c8ef-490a-b3ea-d5257f45dd85	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 07:53:36.009051+00	
********-0000-0000-0000-************	02d1c03b-8332-4a4e-981e-2ca81a48bc5c	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 08:52:10.708364+00	
********-0000-0000-0000-************	cdb9b3eb-9cc3-4be6-9ee4-cb2f4d178e59	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 08:52:10.710575+00	
********-0000-0000-0000-************	fd805fd0-51e9-46d0-8919-e8d905f6bb41	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 09:50:54.494182+00	
********-0000-0000-0000-************	3559753e-3840-4058-982a-ce1851e1426f	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 09:50:54.495569+00	
********-0000-0000-0000-************	bac9a3fa-22b1-4a4a-8faa-d986fda24522	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 11:11:01.564115+00	
********-0000-0000-0000-************	f7550a5a-dbf2-469a-a185-f6a97c78811a	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 11:11:01.565111+00	
********-0000-0000-0000-************	8e29daf0-5f63-4eab-8d35-74454d810611	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 12:12:34.829603+00	
********-0000-0000-0000-************	f8037585-8316-4c5a-b83b-f79f6b467ca7	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 12:19:57.05364+00	
********-0000-0000-0000-************	e77398f0-8edc-407c-bc7c-442b4a1d83be	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 13:05:23.385304+00	
********-0000-0000-0000-************	********-0b0b-4992-9403-eeeb88cfd1f5	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:10:49.543408+00	
********-0000-0000-0000-************	9dc93a97-bc6c-47f6-97a0-9a792d158996	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:10:49.546349+00	
********-0000-0000-0000-************	7e3cc04a-b6f7-4724-848f-71651cecbdf3	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:12:10.614957+00	
********-0000-0000-0000-************	e9544e23-6d35-4fea-b133-328391461e15	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 13:12:10.616939+00	
********-0000-0000-0000-************	1c93c87f-626f-41b7-a568-f14822f8ab07	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 13:29:52.330901+00	
********-0000-0000-0000-************	5088f244-ad01-4f76-968b-fcfc9aed5493	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:20:23.577765+00	
********-0000-0000-0000-************	0740fd39-0fcf-4d4e-90d7-a3141e9ad8f5	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:20:23.580347+00	
********-0000-0000-0000-************	5bfeed03-8424-41f7-9681-814a2041a61b	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:26:43.709338+00	
********-0000-0000-0000-************	45f66b6d-6a7a-4447-ae8e-5c9e23e9db13	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:26:43.710892+00	
********-0000-0000-0000-************	acfce274-ec95-4572-b0eb-777fa8ee42c9	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:29:06.240618+00	
********-0000-0000-0000-************	2e13ea87-c8cc-454b-8378-1e75b446aaaa	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 14:29:06.24204+00	
********-0000-0000-0000-************	ff772a81-d7aa-41dd-b7ac-8b17bc4325ea	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-15 14:52:08.874759+00	
********-0000-0000-0000-************	ee7cda11-1510-423b-8e18-d0e9ff6c7842	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:24:45.699+00	
********-0000-0000-0000-************	529fc884-bc8d-4bfc-b9b2-24b054fc9842	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:24:45.700331+00	
********-0000-0000-0000-************	93acd484-c9d6-4e12-87e8-ec3f1a33c65a	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:26:58.520008+00	
********-0000-0000-0000-************	6ae13f8c-78a6-4128-a1b4-1b6a04683071	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 15:26:58.521136+00	
********-0000-0000-0000-************	adc3829d-5214-41d7-a06e-1835390a6b26	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 17:27:51.264042+00	
********-0000-0000-0000-************	7cbe7d75-b155-4ab9-943e-0df98ff9a6de	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-15 17:27:51.265772+00	
********-0000-0000-0000-************	7504d06f-1110-4737-813a-cc78bff7f5e1	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 00:30:36.565888+00	
********-0000-0000-0000-************	068dd850-7d34-46a2-8a99-b1ce512976c6	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 00:30:36.568354+00	
********-0000-0000-0000-************	46abc340-618b-4b52-b932-48c98273fdfb	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 02:34:26.369821+00	
********-0000-0000-0000-************	a1b6801e-4070-43d0-b31b-27bc9129825b	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 02:34:26.371925+00	
********-0000-0000-0000-************	d543132e-28e0-4bff-982a-40ee22e21541	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 03:32:35.2149+00	
********-0000-0000-0000-************	2d4ab280-edf9-4ee4-8270-875e193cd0a1	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 03:32:35.216383+00	
********-0000-0000-0000-************	f8524f40-3130-483c-9fca-78d2aa96286e	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 04:30:41.702554+00	
********-0000-0000-0000-************	b2e8cd27-bd49-44d1-a547-b1b75972efee	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 04:30:41.705658+00	
********-0000-0000-0000-************	4efd82ff-5d8e-4921-ba6f-d4708e882b51	{"action":"token_refreshed","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 05:37:26.969877+00	
********-0000-0000-0000-************	f85b5ca5-831d-4bc4-9d1e-5dc364077b6e	{"action":"token_revoked","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-16 05:37:26.977051+00	
********-0000-0000-0000-************	23ab8143-15a3-45cf-ad60-7a94c9181b6e	{"action":"login","actor_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 09:38:04.233421+00	
********-0000-0000-0000-************	082626d1-a8ad-4a33-a241-6e51692a029a	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"fb65f3a8-652d-46d1-b6ae-4ef148c1c729","user_phone":""}}	2025-06-17 09:38:13.856208+00	
********-0000-0000-0000-************	********-c7fd-40f3-bddc-962bfe875554	{"action":"user_confirmation_requested","actor_id":"72ee36ce-4e0c-433a-b9e4-379ca26e9e2f","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:06:20.899291+00	
********-0000-0000-0000-************	c3ed11af-9d3c-452c-9a80-55cf8e5533ed	{"action":"user_confirmation_requested","actor_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:16:26.902289+00	
********-0000-0000-0000-************	65ef2747-c70b-47c5-8051-0752e553aeec	{"action":"user_signedup","actor_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 10:16:59.668782+00	
********-0000-0000-0000-************	612cd087-1562-45db-8f6f-e6c4daf3b899	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"72ee36ce-4e0c-433a-b9e4-379ca26e9e2f","user_phone":""}}	2025-06-17 10:18:44.752284+00	
********-0000-0000-0000-************	85d796ca-b019-48e9-9a35-fd5eec3f5ba8	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"cf71d586-b5e9-4d7f-8d8c-c7a1fdf0a4b9","user_phone":""}}	2025-06-17 10:19:02.797444+00	
********-0000-0000-0000-************	ffa36b8c-7c5e-4787-b477-9c518738282b	{"action":"user_confirmation_requested","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:19:13.741897+00	
********-0000-0000-0000-************	5f9d919a-5ea1-4e9d-8cc7-f2458d7c408b	{"action":"user_signedup","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 10:19:25.809927+00	
********-0000-0000-0000-************	6eab9ee3-c0ba-4393-960d-f074e7bbf21a	{"action":"user_confirmation_requested","actor_id":"7e960dd3-190b-4e41-b4f6-69ed62e6c54b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 10:20:03.759189+00	
********-0000-0000-0000-************	f70032bd-c13a-4aa1-bf70-1ad24f6e0148	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 11:18:30.792092+00	
********-0000-0000-0000-************	ac0eace7-7480-4c4d-950d-58d09bed033e	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 11:18:30.79771+00	
********-0000-0000-0000-************	867421b3-d63c-432a-80d9-69b875d5d2f0	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 12:17:03.750674+00	
********-0000-0000-0000-************	ccce2fcb-2237-4eaa-9257-8a707fd0848a	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 12:17:03.753577+00	
********-0000-0000-0000-************	2d6bbb2b-02eb-4a18-ae68-ae8d39487042	{"action":"token_refreshed","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 15:48:04.001459+00	
********-0000-0000-0000-************	35a05d98-1ac1-4eb2-a376-4560ab82f7e4	{"action":"token_revoked","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-17 15:48:04.021997+00	
********-0000-0000-0000-************	abb9d3c8-872f-44b6-843c-6768bc446132	{"action":"login","actor_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 15:48:08.220376+00	
********-0000-0000-0000-************	1e41481d-c45c-4419-bdd2-ef0a5e3da6bd	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"9593be09-f3f5-455b-b4c0-b5b5ef09ef13","user_phone":""}}	2025-06-17 16:22:03.711209+00	
********-0000-0000-0000-************	6a0968ad-5973-4c50-9ecb-5ddf314091f5	{"action":"user_confirmation_requested","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-06-17 16:22:20.14148+00	
********-0000-0000-0000-************	0c00a8e2-bb04-4fad-b07f-626ebfa32c35	{"action":"user_signedup","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-06-17 16:22:33.719386+00	
********-0000-0000-0000-************	a608a8b3-9bc3-46cd-a93e-87d76013e6c4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-17 16:22:39.237673+00	
********-0000-0000-0000-************	57cb12a1-ea5c-477c-84e8-a407385544c1	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"7e960dd3-190b-4e41-b4f6-69ed62e6c54b","user_phone":""}}	2025-06-18 00:26:56.226143+00	
********-0000-0000-0000-************	c728d7bd-77a3-4da5-b6a7-c176551636d0	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:38:28.269681+00	
********-0000-0000-0000-************	b96c276a-c642-41b1-9d43-934c530fac48	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:40:10.589356+00	
********-0000-0000-0000-************	49e6370f-c07b-4ed3-9739-1eb7a4cce246	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 03:40:40.420176+00	
********-0000-0000-0000-************	1b6bb7c9-1ac0-41e3-84ad-dd4f1466ee61	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:42:18.198278+00	
********-0000-0000-0000-************	1f8134a2-09b4-465d-8bb1-3762667e8d43	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:43:48.762941+00	
********-0000-0000-0000-************	71581f8c-4f76-49fc-8831-a43d32a421f0	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:46:25.456128+00	
********-0000-0000-0000-************	228ff4a4-e0c5-4b42-86aa-992c7d13452f	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 04:52:37.602444+00	
********-0000-0000-0000-************	a8def8ec-423d-424f-8fed-0005cbd48deb	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-18 05:09:19.831031+00	
********-0000-0000-0000-************	78ed945a-ee84-49d6-a02f-41f19afc8b5a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 06:19:48.608256+00	
********-0000-0000-0000-************	f3bff03c-3cd4-4458-9af7-fe37d13ba362	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 06:19:48.611989+00	
********-0000-0000-0000-************	9ed06b81-a517-4626-8207-86214221c1b9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 07:19:33.932118+00	
********-0000-0000-0000-************	a24e0aab-cb45-4263-bd6f-c49e046833a7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 07:19:33.934249+00	
********-0000-0000-0000-************	0a062f3b-75c7-4ac4-8e16-06f8ae5bf72a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 08:37:27.605794+00	
********-0000-0000-0000-************	2f146873-f3c7-42ba-a645-dad27e976226	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 08:37:27.607223+00	
********-0000-0000-0000-************	73116942-1a05-4266-9f37-fb15aa1fb5b7	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 09:36:58.21853+00	
********-0000-0000-0000-************	04ced8f1-7b15-4fc4-a70e-e00d5e91b38a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 09:36:58.220933+00	
********-0000-0000-0000-************	ca3edce1-1ba1-4f51-ac90-6f2628105470	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 16:14:54.284405+00	
********-0000-0000-0000-************	7ad0d9a4-b84b-421c-9dbb-02f67201eb1c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 16:14:54.304282+00	
********-0000-0000-0000-************	b7e1b532-1b83-4196-93fb-333d3091077e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 17:13:36.371695+00	
********-0000-0000-0000-************	5dc3d7fc-2713-4453-8d47-76e857517b69	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 17:13:36.37544+00	
********-0000-0000-0000-************	48a85885-ce45-4ccd-8ae8-5b93f50c25bc	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 20:19:59.293257+00	
********-0000-0000-0000-************	e634b2f1-145d-4f35-8909-7e28392bb5df	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-18 20:19:59.303407+00	
********-0000-0000-0000-************	b01d462c-87b9-4eea-a292-c75efb208fbb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 02:25:35.107273+00	
********-0000-0000-0000-************	ff97451d-fedd-40a3-b65f-0f87fb60f31c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 02:25:35.117625+00	
********-0000-0000-0000-************	5a891525-ff48-43d4-baa9-6938c690d42e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 03:23:49.586842+00	
********-0000-0000-0000-************	e024dc3d-072d-4a00-9fe1-e2f6bc469f80	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 03:23:49.590432+00	
********-0000-0000-0000-************	53b6ae03-92ab-4631-a33e-f6c52fa4c62a	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 04:49:31.024942+00	
********-0000-0000-0000-************	9ca200dc-0124-481a-9dce-b8ef994d6970	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 04:49:31.033004+00	
********-0000-0000-0000-************	c2cb6724-7f9d-46d5-b72f-db46ec438f34	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 04:49:36.429638+00	
********-0000-0000-0000-************	50f521b4-702a-42de-b5c8-c529f538f610	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 04:49:41.599975+00	
********-0000-0000-0000-************	ae199886-ad04-4a27-86a0-d756b59a13ed	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 06:28:35.966455+00	
********-0000-0000-0000-************	8123d5ae-a0ad-43ab-8623-c277ac3425db	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 06:28:35.969648+00	
********-0000-0000-0000-************	b9f0ed7e-b567-4c45-98ef-63a13f9603a9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 07:27:11.434037+00	
********-0000-0000-0000-************	aa7439c5-8aa9-416d-bafc-32b78b81313a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 07:27:11.436882+00	
********-0000-0000-0000-************	7594a8c2-98af-47d4-9a27-c00fc84042bf	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 09:36:57.82196+00	
********-0000-0000-0000-************	e2186654-172d-40ba-9791-8dde38537ee8	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 09:36:57.830936+00	
********-0000-0000-0000-************	d7a542b6-14d1-4b69-913b-46f57935bdde	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 10:35:30.524071+00	
********-0000-0000-0000-************	8d6b5c5a-9fb4-433c-9cfd-898de39150d5	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 10:35:30.526412+00	
********-0000-0000-0000-************	43eed376-06ea-4938-917f-5342ea0b8772	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 11:46:08.180731+00	
********-0000-0000-0000-************	65726a24-e842-42b2-88f7-9d0c02c91a12	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-19 11:46:08.183298+00	
********-0000-0000-0000-************	********-32b6-4b12-afea-f28bba559f17	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:33:53.144896+00	
********-0000-0000-0000-************	8493a5cc-d8cb-4537-9ef1-37dcfcbd7a91	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:37:59.873046+00	
********-0000-0000-0000-************	da951d37-5991-44c8-ba31-feb2cc13b497	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:38:04.758649+00	
********-0000-0000-0000-************	********-9f6c-42ef-b810-82c5cb9ef8ed	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:38:08.518352+00	
********-0000-0000-0000-************	17df229a-5461-498b-9b90-0b03cff0bf2c	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:39:24.201605+00	
********-0000-0000-0000-************	3d507fff-9452-4975-9308-f4dfbc03e725	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-19 12:40:06.719862+00	
********-0000-0000-0000-************	b2304539-2c4c-435c-b5ea-78e1fbed0d45	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-06-19 12:40:17.635907+00	
********-0000-0000-0000-************	b240c1ed-8f5d-43b4-8edb-ae8823a4f3c4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-06-20 00:02:13.927482+00	
********-0000-0000-0000-************	47041bb0-5242-422c-868c-161be540a601	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-20 01:01:47.147652+00	
********-0000-0000-0000-************	fe92bef0-c283-4ee9-bbda-12974f27a319	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-06-20 01:01:47.150476+00	
********-0000-0000-0000-************	95d40bfc-b84f-490c-8192-e7ca4e4ceadc	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:05:56.812258+00	
********-0000-0000-0000-************	b449c0d2-4ed1-41db-a403-8c588f8e5cc3	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:12:00.564394+00	
********-0000-0000-0000-************	7beb55f1-9caa-45ab-b7f4-5659966bfa10	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-18 08:15:09.376264+00	
********-0000-0000-0000-************	1c953078-d077-45ca-96b6-59e6814cc28e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 04:14:29.887569+00	
********-0000-0000-0000-************	6db79546-2d09-4a38-a8cb-61321333cda6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 04:14:29.908415+00	
********-0000-0000-0000-************	15ccd53d-de50-4b3f-8f40-26f8ce5da72f	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 04:14:44.881371+00	
********-0000-0000-0000-************	c1077df5-da8d-44af-83fb-f7557efff4d8	{"action":"user_confirmation_requested","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 04:14:54.992032+00	
********-0000-0000-0000-************	afda2a7c-658e-4972-a3ad-27c1f5c69f73	{"action":"user_signedup","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 04:15:50.603201+00	
********-0000-0000-0000-************	387012db-93dc-4e5e-8f2c-3da27d215d20	{"action":"logout","actor_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 04:23:54.679434+00	
********-0000-0000-0000-************	94f9d626-6129-4a35-a817-9ee5d09191ed	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 04:24:01.163263+00	
********-0000-0000-0000-************	3bd079d8-1eba-4f54-8c58-191de17fa6e5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 05:30:34.656716+00	
********-0000-0000-0000-************	cdadea53-feed-4dd2-8697-f7ac1bf9d3a6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 05:30:34.66559+00	
********-0000-0000-0000-************	013a8d65-2e34-42ff-86c6-faca616e270c	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:06:30.195413+00	
********-0000-0000-0000-************	43ed51d2-25dd-44ae-8e69-35bcadaa87da	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"cac9897f-3814-4a5c-8890-79b2e35c52a8","user_phone":""}}	2025-07-19 06:08:09.688961+00	
********-0000-0000-0000-************	55f651f0-1ade-46a4-b0b9-95c522c5d0b8	{"action":"user_confirmation_requested","actor_id":"6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:08:35.014848+00	
********-0000-0000-0000-************	3ca09e7b-b35c-491d-a6d5-d47151ce637c	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf","user_phone":""}}	2025-07-19 06:10:54.694761+00	
********-0000-0000-0000-************	ab6c0114-58df-4c70-8385-7e44c6564fdf	{"action":"user_confirmation_requested","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:11:52.101419+00	
********-0000-0000-0000-************	6255dac7-ba84-4df2-847e-174d47aae913	{"action":"user_signedup","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:12:11.403672+00	
********-0000-0000-0000-************	299e338a-ea8e-449f-aff7-cdde810f6bf9	{"action":"logout","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:15:31.236383+00	
********-0000-0000-0000-************	0fad88d1-954b-4786-8c99-86bd4cc412c8	{"action":"user_repeated_signup","actor_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:15:37.071983+00	
********-0000-0000-0000-************	f38693f3-b8bc-461f-888a-23db40ccf896	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"27ba2b34-328a-4e06-919a-3f97cb46fd30","user_phone":""}}	2025-07-19 06:15:59.338521+00	
********-0000-0000-0000-************	57d8147d-41d0-4ddf-862e-da4ac4d837a2	{"action":"user_confirmation_requested","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:16:02.145911+00	
********-0000-0000-0000-************	99b6b512-a46d-47d1-a0d4-b43e91b21f91	{"action":"user_confirmation_requested","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:17:47.296616+00	
********-0000-0000-0000-************	9dc44049-0d8e-4ad4-947f-d1a33b92bde5	{"action":"user_signedup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:18:43.646055+00	
********-0000-0000-0000-************	e816c437-207c-4945-b57b-5be8f1476a83	{"action":"logout","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:20:39.5893+00	
********-0000-0000-0000-************	9ed69ad7-ec92-452e-841c-67a4c9ab7aed	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:20:48.323036+00	
********-0000-0000-0000-************	4dc04888-d087-44a5-8a3b-dfb623b31a22	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:30:28.681872+00	
********-0000-0000-0000-************	a86cddd4-d1f3-4725-979f-b946c2bf01c8	{"action":"user_repeated_signup","actor_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:30:36.785145+00	
********-0000-0000-0000-************	195ac2ec-4800-450b-b83e-6e1137c386ac	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 06:31:40.979487+00	
********-0000-0000-0000-************	40eb83bf-f0e1-4f81-9c5e-2b55aaf39d3b	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 06:31:46.883554+00	
********-0000-0000-0000-************	ac803cd2-e190-440d-9400-0e12ca9aa6bd	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"ea6c9640-8770-4ea8-ae82-5e2a178245d5","user_phone":""}}	2025-07-19 06:31:55.883455+00	
********-0000-0000-0000-************	91fed1e2-1c03-441d-911b-eb9845ad6f74	{"action":"user_confirmation_requested","actor_id":"5dbe8b46-856d-48d8-ba4b-086ac2bcea4a","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:32:05.376613+00	
********-0000-0000-0000-************	5919fe64-bf44-4352-998a-8d9ad8942fa9	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"5dbe8b46-856d-48d8-ba4b-086ac2bcea4a","user_phone":""}}	2025-07-19 06:35:05.337698+00	
********-0000-0000-0000-************	29f796fd-3941-4ce9-925f-a7c9fb6353ad	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:35:11.945348+00	
********-0000-0000-0000-************	ff863975-2156-49c7-9735-8d1bc1a8daac	{"action":"user_repeated_signup","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:35:55.423094+00	
********-0000-0000-0000-************	46b9d010-3d2c-4a7b-96d7-302b8069bdd7	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:38:12.661703+00	
********-0000-0000-0000-************	36e4ff9a-9120-4a61-b86a-a4267ccf1622	{"action":"user_confirmation_requested","actor_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:39:56.301387+00	
********-0000-0000-0000-************	07731f49-c7ef-4393-8532-9eeb6d8b5fd5	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:41:32.545455+00	
********-0000-0000-0000-************	2774d954-c741-4143-88b4-ad4415acd72a	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-07-19 06:42:34.560392+00	
********-0000-0000-0000-************	2ad15a82-ab14-48e3-8435-2b51cbd71f23	{"action":"user_confirmation_requested","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user"}	2025-07-19 06:43:43.849419+00	
********-0000-0000-0000-************	fa9cd60f-7e6d-4cd6-8bd3-64d002e65722	{"action":"user_signedup","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-07-19 06:43:57.902736+00	
********-0000-0000-0000-************	8b74f2be-b402-49a2-9cbd-61c6a7c6d44c	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 09:55:21.966346+00	
********-0000-0000-0000-************	a507f4ab-b7ec-4be7-8401-6d9bbe55624c	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 09:55:22.240999+00	
********-0000-0000-0000-************	0932ba9c-9609-475d-af76-e30c8a627871	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-19 09:57:13.904658+00	
********-0000-0000-0000-************	3d2a9515-7a16-4db8-9bc1-60dde9d9c4fe	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-19 09:57:17.88589+00	
********-0000-0000-0000-************	ce163596-69b6-4424-b5e9-a0ba290456f1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 14:40:33.119276+00	
********-0000-0000-0000-************	ae9adad1-d8e0-4b3d-9a6d-fc437b2a4ddf	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 14:40:33.129095+00	
********-0000-0000-0000-************	14e9e329-fe24-48b6-ba5e-e1008c504c6b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 16:27:53.83693+00	
********-0000-0000-0000-************	e5c2206a-646b-4551-9041-72436518750c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 16:27:53.848255+00	
********-0000-0000-0000-************	86b33c31-0244-4b8d-84fd-d7c2829a53f4	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 17:26:39.840266+00	
********-0000-0000-0000-************	7122e278-d5e3-4df8-b6fc-72d55be3285e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-19 17:26:39.84393+00	
********-0000-0000-0000-************	17056bde-6479-42cd-a11f-81e8d8251a16	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-20 01:05:20.031541+00	
********-0000-0000-0000-************	effd3021-743a-4157-9480-abfa57257da3	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-20 01:05:20.048356+00	
********-0000-0000-0000-************	a954b620-4775-4b36-8e21-fd119b7ac8b0	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 01:42:51.702333+00	
********-0000-0000-0000-************	899cad8f-2799-48ef-a7e7-0a3397d152e6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 01:42:51.713954+00	
********-0000-0000-0000-************	e2f34407-fc24-4f91-b5a0-962224616d4d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 04:22:11.935373+00	
********-0000-0000-0000-************	88b8b5ba-a0a1-4c10-b993-6a5e8a305535	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 04:22:11.938891+00	
********-0000-0000-0000-************	c32f82ef-1660-4f1a-9dc4-febf5183b77e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 05:21:45.904527+00	
********-0000-0000-0000-************	9c473525-933c-4fb9-b161-2245dcccc708	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 05:21:45.910591+00	
********-0000-0000-0000-************	8ab01a96-7c47-46bd-8cf6-7682509d9ed5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 06:21:27.205527+00	
********-0000-0000-0000-************	768f7e12-f5c8-4d9c-989a-b8541f0716d1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 06:21:27.218148+00	
********-0000-0000-0000-************	79f54cde-834f-4f67-8215-608a31e533e9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 07:20:09.077369+00	
********-0000-0000-0000-************	72877034-fa80-4b91-a5da-ef1935cebf16	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-22 07:20:09.080203+00	
********-0000-0000-0000-************	5731f27c-f7ea-45a9-997d-f7c3ff9fa8b3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 02:40:38.97482+00	
********-0000-0000-0000-************	0e9e8e3e-9124-44d0-8c6c-d6fafc048475	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 02:40:38.984078+00	
********-0000-0000-0000-************	ad501f29-3a23-4ed0-96dd-129688e7044b	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-24 03:19:53.620444+00	
********-0000-0000-0000-************	c2ed3b5d-cb53-4b95-b35e-4ee8c6308e75	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 04:19:07.525455+00	
********-0000-0000-0000-************	eb3a52e4-fef4-460c-bdec-************	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 04:19:07.528513+00	
********-0000-0000-0000-************	7b2e9357-83c4-4b38-9ad3-b1da3d365171	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:18:55.845963+00	
********-0000-0000-0000-************	940d801f-390d-49a9-bdeb-12aa82137b7a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:18:55.850456+00	
********-0000-0000-0000-************	edfe543c-46de-48e7-be90-c68bd308b42b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:20:25.52825+00	
********-0000-0000-0000-************	f6acf915-4b8d-41d4-9c3a-bbac96a787f7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-24 05:20:25.529358+00	
********-0000-0000-0000-************	2e92ce57-22d1-4826-9bfd-a42bfc4ddbc2	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:00:26.940443+00	
********-0000-0000-0000-************	98758d60-3886-4ab8-994e-f0e97f03dfec	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:00:26.955352+00	
********-0000-0000-0000-************	8012ca84-ae5c-4e8e-8801-8933fe28e452	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:51:02.553253+00	
********-0000-0000-0000-************	34158385-6abe-4e43-9b2f-5d1fa0d09fe0	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 02:51:02.566068+00	
********-0000-0000-0000-************	0e563650-e032-4d6f-b085-1166da7c6766	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 03:50:10.131095+00	
********-0000-0000-0000-************	b6649c1d-5311-4789-b2a0-ccee22bf2e04	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 03:50:10.134117+00	
********-0000-0000-0000-************	d98521bc-0ba0-47ec-82d1-673fa618863d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 06:26:10.513977+00	
********-0000-0000-0000-************	150b56ed-b1d0-47ef-b7da-ecc4176efd47	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 06:26:10.523619+00	
********-0000-0000-0000-************	ac8cad3d-daea-466e-a170-1abd4dd3eae6	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 07:56:29.805975+00	
********-0000-0000-0000-************	82eb831d-3046-44c1-8069-b4ae6db6b25b	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 07:56:29.81174+00	
********-0000-0000-0000-************	801e2dce-2bd5-467a-a7c2-505d152583eb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 08:54:41.626568+00	
********-0000-0000-0000-************	e6be564f-ea33-4b99-9f0c-0c15e28e1758	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 08:54:41.629208+00	
********-0000-0000-0000-************	be603f15-1dae-465c-b2a1-6455d7229e70	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 10:26:05.728804+00	
********-0000-0000-0000-************	3c4bf075-11ac-4466-a092-174db9bf96f9	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 10:26:05.735511+00	
********-0000-0000-0000-************	7c9462aa-328d-4076-9d6d-5f70345c0f8c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 13:15:34.640973+00	
********-0000-0000-0000-************	86abbcac-366c-4603-a6bb-0745c8055237	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 13:15:34.653616+00	
********-0000-0000-0000-************	425db652-b0b9-41d2-8b30-6ed3425c6237	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 14:41:47.143758+00	
********-0000-0000-0000-************	781b059e-7be8-42ee-bbed-62975a702ca1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 14:41:47.171345+00	
********-0000-0000-0000-************	6204a742-7223-47ac-b049-be75006e787c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 15:52:20.754684+00	
********-0000-0000-0000-************	4ab964a6-54b5-4037-8250-bc39a9ff804f	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-25 15:52:20.757933+00	
********-0000-0000-0000-************	f146a5bf-8216-4b4b-8030-57c28dbe36f3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 01:12:28.885474+00	
********-0000-0000-0000-************	e0be6c7c-06c4-4fed-bf1b-d2eb66f78681	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 01:12:28.900273+00	
********-0000-0000-0000-************	22b4f28e-d6e1-4346-8024-d4dc3f87a076	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 03:54:55.089296+00	
********-0000-0000-0000-************	dacacb75-5fe0-460c-8191-238c2b835349	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 03:54:55.094303+00	
********-0000-0000-0000-************	84ee2f7a-6e7a-4362-88d3-373aebdf566e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 06:37:46.019891+00	
********-0000-0000-0000-************	75ff1dbe-84a4-435d-92af-6727427d712e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 06:37:46.022039+00	
********-0000-0000-0000-************	57dd56a6-c5fd-4b94-bfb7-d77e4103b412	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:44:44.220135+00	
********-0000-0000-0000-************	3a25890a-ff2c-46d2-9ac0-5247fbee02d4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:44:44.221998+00	
********-0000-0000-0000-************	70c18382-78a6-4b1e-bb79-4638ea8a4d60	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:50:53.628723+00	
********-0000-0000-0000-************	30b6a925-1eba-4a4a-b31f-8d4553224c5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 07:50:53.631506+00	
********-0000-0000-0000-************	28a3f881-44fe-40e0-9d0d-3154542965c9	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 08:49:21.657199+00	
********-0000-0000-0000-************	f1a51159-b37a-4726-a1ca-12ee8a1e26db	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 08:49:21.659388+00	
********-0000-0000-0000-************	39a3474b-a8bc-4c6f-8c4b-b5829ebdc26d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 09:50:36.966466+00	
********-0000-0000-0000-************	7cff0ae1-e1f5-48f4-8402-fcc7c8d044b7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 09:50:36.973352+00	
********-0000-0000-0000-************	4c75ed7a-b795-4777-8b29-033e0bbc8713	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 10:49:04.047533+00	
********-0000-0000-0000-************	2970589a-a849-4978-a8d3-89abb8c61640	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 10:49:04.051293+00	
********-0000-0000-0000-************	4546553f-7e40-48f9-a2d9-8ae4bcaae429	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 11:47:28.948204+00	
********-0000-0000-0000-************	5ba67d4d-2163-42d4-b625-35072d099be7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 11:47:28.950699+00	
********-0000-0000-0000-************	20ff1550-ec49-402d-b6e0-71d72be77134	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 15:56:52.635417+00	
********-0000-0000-0000-************	b29bf21b-98b6-4c36-afa3-7e725b4e9733	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-26 15:56:52.650305+00	
********-0000-0000-0000-************	f7a8db63-c42d-49ab-a318-7e71ea2f5b45	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-27 18:36:22.4073+00	
********-0000-0000-0000-************	f2ab6bc8-c4d7-4d49-9bdc-4b780189f54a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-27 18:36:22.42236+00	
********-0000-0000-0000-************	d720f8a1-53ad-4db7-a273-699abde6c014	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 03:36:19.158771+00	
********-0000-0000-0000-************	50df4dda-a652-419e-95d6-82b5d1c4e9a7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 03:36:19.16565+00	
********-0000-0000-0000-************	8d53a775-5d93-463e-8a6b-c366c19bfecb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:24:04.628971+00	
********-0000-0000-0000-************	7cfd4607-d4a2-4624-a3eb-a228e6a034e4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:24:04.632368+00	
********-0000-0000-0000-************	efb06b78-5fbc-4dc8-907e-eb1cf69a9359	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:35:08.482223+00	
********-0000-0000-0000-************	df1dfe77-d28a-4644-ac46-dfb7431f8fae	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 04:35:08.484139+00	
********-0000-0000-0000-************	42a18973-86de-4128-a8d4-2da3c5e471e3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:15:13.185069+00	
********-0000-0000-0000-************	ac37203d-30f1-41eb-97f8-1e9b70d71b3d	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:15:13.194648+00	
********-0000-0000-0000-************	d9333ff6-77f3-4010-b387-196023bee3c1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:56:03.246657+00	
********-0000-0000-0000-************	7e911aac-2261-4855-9264-41a243cfec17	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 06:56:03.249047+00	
********-0000-0000-0000-************	6e90acdf-87a1-496b-a4e0-8dd5ce9fd9b1	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:17:46.45108+00	
********-0000-0000-0000-************	31e0772c-af3b-45d8-b79e-8df1b9c13fe3	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:17:46.451959+00	
********-0000-0000-0000-************	3a76912d-f436-4755-89e5-d6f3f55895c4	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:55:25.302646+00	
********-0000-0000-0000-************	29209543-8ed5-4663-b58c-55db00d92905	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-28 07:55:25.305005+00	
********-0000-0000-0000-************	709ef3ee-7c84-4539-874b-082f605fb031	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 04:11:57.252701+00	
********-0000-0000-0000-************	e346f457-89d8-4464-a9aa-25bd829bdcc1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 04:11:57.267548+00	
********-0000-0000-0000-************	f2a671ad-71a1-40b1-97ec-fa9f04762f2b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:32.147295+00	
********-0000-0000-0000-************	a793b5de-09c0-4ed9-83d4-c8ffc7168d25	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:32.159346+00	
********-0000-0000-0000-************	4d8592d7-04b7-4f2a-a43c-ac454c373844	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:42.719847+00	
********-0000-0000-0000-************	26a1b18a-32b7-4d03-90ee-59481593cfeb	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 11:42:42.720457+00	
********-0000-0000-0000-************	2545ee99-bc32-4d4f-96b9-96abf9909855	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 12:41:14.400879+00	
********-0000-0000-0000-************	b3ad6275-2cc0-4e77-b49e-3d10d2a0bb6c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 12:41:14.405107+00	
********-0000-0000-0000-************	c61772d1-c019-43ed-9c62-d90ca63d4a53	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 13:39:29.232386+00	
********-0000-0000-0000-************	ff349122-6f77-47cf-a935-c9937327cc0a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 13:39:29.235029+00	
********-0000-0000-0000-************	c76b20d7-410b-480a-8b75-b779fa71a078	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 14:43:02.585406+00	
********-0000-0000-0000-************	2e7f1661-1178-4137-ae1c-c305d351f708	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 14:43:02.591927+00	
********-0000-0000-0000-************	c2546e7e-aa1a-4598-8f06-fe6934e5c673	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 18:55:12.082407+00	
********-0000-0000-0000-************	e9a30e63-65b4-4fea-b1b6-0cfb1f9d90f4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-29 18:55:12.09534+00	
********-0000-0000-0000-************	34f9d08a-fb18-4e68-befe-7a7d6a09c69d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 06:34:31.190906+00	
********-0000-0000-0000-************	65238b84-fdd8-401d-b0fa-bed592075a26	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 06:34:31.206639+00	
********-0000-0000-0000-************	0962e1d0-4453-483f-950e-e2f73f1fc374	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 07:34:30.696853+00	
********-0000-0000-0000-************	2882ed5c-4fb3-417b-870f-2c501daedcaf	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 07:34:30.705172+00	
********-0000-0000-0000-************	322c1d63-19a4-4d1b-afa2-6787f97401f4	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-30 07:41:49.3608+00	
********-0000-0000-0000-************	3c9dcc82-ec68-4ab7-8c8c-a91f110e5397	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 08:33:08.445235+00	
********-0000-0000-0000-************	e9f1a020-1985-4897-a6bc-4e16bb3ca368	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 08:33:08.448034+00	
********-0000-0000-0000-************	a0897947-d93e-40ed-b40c-45d02b7fd0e6	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:42.513023+00	
********-0000-0000-0000-************	3b2b658a-77ee-4ff4-b7e1-978b9344a9a6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:42.528981+00	
********-0000-0000-0000-************	f7c9a01a-ce22-40ee-867d-d6d74a0a3b4d	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:48.264932+00	
********-0000-0000-0000-************	44c567f9-9d41-4800-aa0c-d96334e6ec0a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 09:40:48.265639+00	
********-0000-0000-0000-************	9d067f20-cd3c-4a2a-8d3c-f2db0d66bebc	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:39:27.738067+00	
********-0000-0000-0000-************	dd4e20c6-7759-4492-858e-90793aff20f1	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:39:27.742972+00	
********-0000-0000-0000-************	6f330ca4-6562-4eeb-87b5-4f61db2278ae	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:54:12.786425+00	
********-0000-0000-0000-************	97dde85b-2f88-40f6-983c-16d7b242c1de	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 10:54:12.788167+00	
********-0000-0000-0000-************	2f08a2e1-52fe-4435-9ddb-b9aa1979e30c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 12:10:52.394375+00	
********-0000-0000-0000-************	dc37010d-0806-4960-87a4-8b915a947c49	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 12:10:52.39969+00	
********-0000-0000-0000-************	d7476152-72f6-4d04-8545-cd1a1b86105c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:22:48.833639+00	
********-0000-0000-0000-************	5f7db344-0a0e-4d11-b039-3c6a39367338	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:22:48.844224+00	
********-0000-0000-0000-************	5832932d-1a99-421b-95cd-9cf9b164ce11	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:40:36.014792+00	
********-0000-0000-0000-************	d1c9ac07-131c-41c6-99b2-ae72b36adfdd	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 15:40:36.017072+00	
********-0000-0000-0000-************	415f3041-6f97-4d83-96bf-5559a9bc1353	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:21:28.405756+00	
********-0000-0000-0000-************	0c066fb2-630a-48c5-83c3-94abcbdfbe5d	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:21:28.408533+00	
********-0000-0000-0000-************	eea4440e-e993-4490-b7ce-29dfa5fdfaeb	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:43:58.965176+00	
********-0000-0000-0000-************	e4778752-1e54-4e21-919c-87bb8e054cc7	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 16:43:58.968706+00	
********-0000-0000-0000-************	61db99a5-9b76-4631-93e2-ffd3f2fc0662	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 17:36:36.807543+00	
********-0000-0000-0000-************	526d27ce-3fd8-43e1-93af-df0c4e56896b	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 17:36:36.810841+00	
********-0000-0000-0000-************	a6e94e99-ad9d-4def-b9d2-f7150e939a7c	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 18:21:12.904441+00	
********-0000-0000-0000-************	a9d0a984-22c9-4990-834b-d75cc5a85c5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-30 18:21:12.907125+00	
********-0000-0000-0000-************	a8d0b7cd-3ce1-4b78-95dd-158866e74e75	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 09:22:43.329238+00	
********-0000-0000-0000-************	e2f513b7-b3dd-4dcd-ae3b-5f58300f3b5e	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 09:22:43.346142+00	
********-0000-0000-0000-************	12328380-3323-42f6-8317-9b7513aaf397	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 12:40:56.27339+00	
********-0000-0000-0000-************	ba1e5894-ac22-41bb-985d-ec9124b3a7d4	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 12:40:56.291265+00	
********-0000-0000-0000-************	eb3573d8-e0dc-4f86-ac47-946f19683634	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 13:24:22.033551+00	
********-0000-0000-0000-************	42e957df-fc3d-4504-8507-af6418064c3f	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 13:24:23.95889+00	
********-0000-0000-0000-************	402d20f3-e98a-4b96-a52c-39174020eeee	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 13:30:51.231011+00	
********-0000-0000-0000-************	3c00b597-6536-4e50-8174-8e726b2ffd15	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 13:30:55.502867+00	
********-0000-0000-0000-************	e78687a6-607c-4ce4-b57b-404a09914926	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 14:57:52.451588+00	
********-0000-0000-0000-************	efa9ead3-eb21-45d5-a6d6-d6f2d2d7b6d9	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 14:57:52.454761+00	
********-0000-0000-0000-************	1a1172a5-dddd-4a7f-85a6-8119d9b205d7	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:15:23.738902+00	
********-0000-0000-0000-************	5e3f5167-7d89-4c0c-96fb-8c1a6cd5f83d	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:15:38.710367+00	
********-0000-0000-0000-************	baeb32d2-7b6f-4b27-8b37-ec5a78f6f11a	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:16:16.870045+00	
********-0000-0000-0000-************	3b2ba0f2-b1d8-4068-8312-7b33f0f33b08	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:16:20.311118+00	
********-0000-0000-0000-************	17dab7f3-dd4d-4209-a390-a5c3d7be2876	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:22:08.959641+00	
********-0000-0000-0000-************	3e3016dc-92f6-4e27-b5d1-427ed8581a06	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:22:18.575229+00	
********-0000-0000-0000-************	fa460dac-a7ef-4a95-8807-5b8b8a87d5c1	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 15:31:49.520293+00	
********-0000-0000-0000-************	8aff75a4-6251-4018-a3c5-cc8ada28ee39	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 15:31:52.595939+00	
********-0000-0000-0000-************	399c6d39-a3d6-4821-b1e0-18a909ccfbd2	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 16:30:22.277001+00	
********-0000-0000-0000-************	16fcf269-7715-45cd-b423-26bc7c6a88c6	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 16:30:22.286461+00	
********-0000-0000-0000-************	ad3d3a5c-628f-480e-b50c-6ddec6a47f9e	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 17:12:14.047105+00	
********-0000-0000-0000-************	7e85b654-a465-4208-b58d-e2a89c7c187c	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 17:12:18.826608+00	
********-0000-0000-0000-************	182fe021-aa1e-4562-81dc-06b95a4ac953	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 18:12:11.909782+00	
********-0000-0000-0000-************	c5bc5999-9210-46dc-a533-6b00bf250985	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 18:12:11.916745+00	
********-0000-0000-0000-************	be47471c-07b7-494a-bbad-c8c712217e07	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 19:55:35.055875+00	
********-0000-0000-0000-************	ad6969c0-f791-4249-ae56-24e36ddbbc8b	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 19:55:35.059543+00	
********-0000-0000-0000-************	88cd37bc-a1de-4179-a078-f76543ea7231	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-07-31 19:56:08.987802+00	
********-0000-0000-0000-************	36b06188-e377-4465-8b94-3f6dfa8e8a23	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-07-31 19:56:13.52577+00	
********-0000-0000-0000-************	3bb645c4-5a33-4938-b8b4-015324c254b5	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 21:15:38.266274+00	
********-0000-0000-0000-************	e14eaaf3-8ba6-48e3-a762-eb57dc44dd14	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-07-31 21:15:38.271679+00	
********-0000-0000-0000-************	6c16f5ae-0a77-4e02-abf9-a741ab44d064	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 06:16:55.273691+00	
********-0000-0000-0000-************	002ef688-8aac-4759-af64-d7c0c0e00f5a	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 06:16:55.294476+00	
********-0000-0000-0000-************	c0125df8-7a5c-48d9-bd8f-34722ff38d8e	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 06:44:38.540728+00	
********-0000-0000-0000-************	7f1a8a12-2408-48a1-a586-6c190557eeec	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 06:44:53.197106+00	
********-0000-0000-0000-************	1fbdbc3d-85cc-4ece-93c2-471d50193191	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 06:47:46.759619+00	
********-0000-0000-0000-************	714567fa-0e1a-48f8-8330-cdea8e0a0fc2	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 06:47:51.982151+00	
********-0000-0000-0000-************	0c144628-b6de-419f-9ae5-eb4c72afbf1b	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 07:46:14.476414+00	
********-0000-0000-0000-************	a58937ce-20fb-416a-ad72-8afb8e3998d2	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 07:46:14.482475+00	
********-0000-0000-0000-************	97b146b7-c858-4e16-bcf6-6fb523256607	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 08:47:15.859443+00	
********-0000-0000-0000-************	eaaffcb9-7a9d-47c1-8937-c50bec6bfe06	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 08:47:15.866392+00	
********-0000-0000-0000-************	5a4d87c2-3b57-46b9-b742-7ec9b3c3f8a1	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 14:08:02.684584+00	
********-0000-0000-0000-************	29706e0d-cbaf-43cf-b1a6-76d59340969e	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 15:15:36.3165+00	
********-0000-0000-0000-************	6de2d4d0-3731-42f3-9b50-649ad898fca2	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 15:15:36.319823+00	
********-0000-0000-0000-************	7f29dd4e-d8e9-4dbe-b0d0-acf22cf4b9d3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 16:48:12.260074+00	
********-0000-0000-0000-************	dcd8897b-6d5f-4536-b63f-02615fd937ee	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 16:48:12.262496+00	
********-0000-0000-0000-************	b62de546-4b00-4c15-8dc9-1b7766263803	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 16:49:07.518692+00	
********-0000-0000-0000-************	e09828e5-e045-4948-bfe9-29eff2edd23d	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 16:49:17.513601+00	
********-0000-0000-0000-************	9399c62a-4b47-4398-b06c-918d90b8299d	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 17:48:57.847613+00	
********-0000-0000-0000-************	950800cd-8313-4649-837a-0587dee19c35	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 17:48:57.850092+00	
********-0000-0000-0000-************	43d21ea4-78ef-47c1-af03-5a0a0adb51e4	{"action":"token_refreshed","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 20:54:15.10846+00	
********-0000-0000-0000-************	318302a9-6fd7-4599-befe-38f7d7e8ca5a	{"action":"token_revoked","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-01 20:54:15.121589+00	
********-0000-0000-0000-************	850df701-29eb-4f4c-a444-a00429f89402	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-01 20:55:14.390234+00	
********-0000-0000-0000-************	0197c733-9347-48bd-a80d-8e1225a082b7	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-01 20:55:21.062985+00	
********-0000-0000-0000-************	9c423438-b301-4638-97b9-b40460a2c957	{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"a364b8e7-dbca-4b58-a3e6-ad2f24684931","user_phone":""}}	2025-08-02 06:07:03.960483+00	
********-0000-0000-0000-************	7446ac8f-f0ec-4d4f-9bb5-b38734b6efe0	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 06:08:58.768166+00	
********-0000-0000-0000-************	7b5ba855-7b77-4afe-837b-a1e8f068a467	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 06:08:58.769753+00	
********-0000-0000-0000-************	6944e54b-9dca-4799-9d99-a688b23370eb	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 06:15:04.404808+00	
********-0000-0000-0000-************	7b30ce2d-b833-4259-b78d-f0a633163b2c	{"action":"user_confirmation_requested","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-08-02 06:15:45.560541+00	
********-0000-0000-0000-************	a1d17903-d723-4ddd-8b9e-73d5ce21db16	{"action":"user_signedup","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-08-02 06:16:52.239163+00	
********-0000-0000-0000-************	58d237e6-b138-4942-9f1c-2cf695715855	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 07:33:21.51283+00	
********-0000-0000-0000-************	85a01b3c-fc82-4d42-bd67-2ba485c715bc	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 07:33:21.519105+00	
********-0000-0000-0000-************	0b7f2ae1-0d0c-4189-9de2-cb28a3895f77	{"action":"logout","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 07:44:44.81839+00	
********-0000-0000-0000-************	b9f5eeba-3ad5-4012-9c03-79b280107e95	{"action":"login","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 07:44:46.760871+00	
********-0000-0000-0000-************	6334906d-7218-4172-8908-037ad84549a3	{"action":"token_refreshed","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 09:47:26.421863+00	
********-0000-0000-0000-************	a1957482-9b53-458e-bf33-c8c47b10c54c	{"action":"token_revoked","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 09:47:26.444133+00	
********-0000-0000-0000-************	4384c918-aa07-4fc3-8162-81484d6f8dbd	{"action":"logout","actor_id":"e432fabd-c944-4c01-9dcd-2e687287c8dc","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 09:49:28.105397+00	
********-0000-0000-0000-************	b1def1a8-1525-41d3-9abf-ce8fb1225454	{"action":"login","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:49:34.351409+00	
********-0000-0000-0000-************	0e0afcab-8612-48c4-a825-bd5aef049ffd	{"action":"logout","actor_id":"fa835c38-a6b7-483b-b030-3bbf279f6a1b","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-08-02 09:50:27.32277+00	
********-0000-0000-0000-************	eab6433e-a8d0-404f-aca7-14110c57d1ae	{"action":"login","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:50:35.581592+00	
********-0000-0000-0000-************	6d1dee31-4d9c-4676-b274-f45fa04645ce	{"action":"login","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-08-02 09:57:19.62737+00	
********-0000-0000-0000-************	a0a5fd10-6815-4367-a833-080ad7e16aa1	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 10:57:31.964814+00	
********-0000-0000-0000-************	3185de67-189d-4a26-8c64-a92ad680182f	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 10:57:31.968391+00	
********-0000-0000-0000-************	881139d3-b8ef-47af-be04-19aa5701d7e2	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 12:20:20.893529+00	
********-0000-0000-0000-************	5d1ebccc-be62-427c-a449-c7466c3ee920	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 12:20:20.903121+00	
********-0000-0000-0000-************	518197fd-ce1c-4208-bb9b-a6fb4dbc88ae	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 14:53:35.364498+00	
********-0000-0000-0000-************	9cecb903-b1ad-43ac-82b7-ad5051291cfb	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 14:53:35.381158+00	
********-0000-0000-0000-************	494b1e5d-95bd-44cc-98ec-6ff0fb49dbcd	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:46:23.026252+00	
********-0000-0000-0000-************	9421e349-6a8d-4e17-bb83-4d2d3da8cc43	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:46:23.034732+00	
********-0000-0000-0000-************	cb9beb14-2436-47a6-8cb8-d801f9b3b068	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:48:06.401776+00	
********-0000-0000-0000-************	ad8c9b82-b87d-4ae5-a4ec-ebd7f98f424c	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 16:48:06.404353+00	
********-0000-0000-0000-************	c3faff26-6f64-4cb9-81dc-2c720115273d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:46:08.472812+00	
********-0000-0000-0000-************	4042ea51-96d3-45c4-b2eb-0fe79bcb2fd4	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:46:08.490566+00	
********-0000-0000-0000-************	d91e1145-8041-4f1b-9584-90f2d82ebd7d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:50:26.284185+00	
********-0000-0000-0000-************	39ee05ea-8856-407c-86c2-52a79963246f	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 17:50:26.287874+00	
********-0000-0000-0000-************	82ce193c-c93a-46dd-b6d3-058b9675c16d	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 18:44:55.76225+00	
********-0000-0000-0000-************	a5c209d1-9993-4605-8cbd-4cb88452d03a	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 18:44:55.764609+00	
********-0000-0000-0000-************	8d7ded1a-37d7-4c7d-be68-af0fae8d39c2	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 20:00:14.434014+00	
********-0000-0000-0000-************	24d845b1-0a5e-4ff2-9c1a-dffc31c56ccb	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 20:00:14.444126+00	
********-0000-0000-0000-************	a1772329-ec09-4942-9ccd-dcf032c94a2a	{"action":"token_refreshed","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 21:02:12.049228+00	
********-0000-0000-0000-************	eaee1835-10dc-42db-9f0c-27980111ed0a	{"action":"token_revoked","actor_id":"fa322dd1-4dad-42a3-b68e-30407d5629b7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-08-02 21:02:12.058544+00	
\.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.flow_state (id, user_id, auth_code, code_challenge_method, code_challenge, provider_type, provider_access_token, provider_refresh_token, created_at, updated_at, authentication_method, auth_code_issued_at) FROM stdin;
0a2cd87c-42ae-4baf-8b03-1231ef45e44e	cac9897f-3814-4a5c-8890-79b2e35c52a8	bf4a597b-eef2-42f8-9514-9e2725cdfc01	s256	KegwoNJ5pa74iw04oLD4wJdGD3_BmSiNnCBso9cx8Bo	email			2025-07-19 04:14:54.992816+00	2025-07-19 04:14:54.992816+00	email/signup	\N
61e07099-b607-498f-ace1-c923bf765827	6e029114-7c26-4dd7-a5e8-4cfe0f16ffaf	9231768f-c524-438d-bf30-2687b3bdbd77	s256	1iJ2kWq5Ocdm-Ai3C-2yAiUvc2JQQzvhuv8vG8cE8rQ	email			2025-07-19 06:08:35.015682+00	2025-07-19 06:08:35.015682+00	email/signup	\N
2aa9c311-0990-4f1e-87d1-fc8361112d7e	27ba2b34-328a-4e06-919a-3f97cb46fd30	5efb701a-25db-4074-8713-221184d6775b	s256	j3vHJwKtrXTjncaq2Z6p-ObdOaCJcdKEiPT0LBbfqVE	email			2025-07-19 06:11:52.101959+00	2025-07-19 06:11:52.101959+00	email/signup	\N
daba3f99-6f6c-40f2-a05f-e24a12ce262d	ea6c9640-8770-4ea8-ae82-5e2a178245d5	be47d397-ce68-4e43-ab92-3acad33dae40	s256	ukUDXTSlNW9vROctCf9vAxyfIGiai7cfXhMAC6lLqcU	email			2025-07-19 06:16:02.14635+00	2025-07-19 06:16:02.14635+00	email/signup	\N
62de1fd9-3d03-48ec-862d-6f1e1188a2b9	ea6c9640-8770-4ea8-ae82-5e2a178245d5	3f8d8fc7-ed65-4189-828f-2b741c949aa2	s256	NoT-KWDP3QiZ6vqSsZEfuAJT_EUsVvvIa-r_NBkDqUM	email			2025-07-19 06:17:47.298782+00	2025-07-19 06:17:47.298782+00	email/signup	\N
47263ecb-cf7a-4120-b995-53f930a9582f	5dbe8b46-856d-48d8-ba4b-086ac2bcea4a	19e30b85-6f49-4f00-a00a-d35df66c0c39	s256	_899SG_JuGUkw3RTHrFeNHKj3RTJ3XUWTJaCS7S9XDI	email			2025-07-19 06:32:05.377225+00	2025-07-19 06:32:05.377225+00	email/signup	\N
6e205194-455f-4961-85a7-6d7260804919	a364b8e7-dbca-4b58-a3e6-ad2f24684931	26637321-d805-4ba6-b62c-ca85598d3e26	s256	-gM403eouqZtE0Z8l1DBmJHHhbnh_im_5YuBL11RfQA	email			2025-07-19 06:35:11.94603+00	2025-07-19 06:35:11.94603+00	email/signup	\N
235637f8-51c8-414c-9ddc-876d23beb6fa	a364b8e7-dbca-4b58-a3e6-ad2f24684931	5796133e-faff-438f-9a9d-e2bf5ffc6440	s256	zBv8wQoUYSHA9Vjql8qo47A90dGl5DN0GlpT5tFKlxY	email			2025-07-19 06:38:12.662442+00	2025-07-19 06:38:12.662442+00	email/signup	\N
8bc2289b-3b43-48f2-b2bf-6b23ad8173a5	a364b8e7-dbca-4b58-a3e6-ad2f24684931	0c668d41-e1e9-437f-8dab-b942eca5337d	s256	zFJERPBN4aQC9ckI00JWohhdR6ZCBbxCvKL2YVuPZ_A	email			2025-07-19 06:39:56.302491+00	2025-07-19 06:39:56.302491+00	email/signup	\N
39a07f9b-302a-4e4d-9bfb-17fd98ea48de	fa835c38-a6b7-483b-b030-3bbf279f6a1b	7aa6b565-e858-4f86-986c-034f02d535e4	s256	yrEXhJwKHjmleh5NWUBAJEODurh1EngfwXytL-LHc0s	email			2025-07-19 06:41:32.546622+00	2025-07-19 06:41:32.546622+00	email/signup	\N
b1510c20-4039-438b-b76f-6eb33f1bbc22	fa835c38-a6b7-483b-b030-3bbf279f6a1b	e4fc5706-cf37-4557-bd7b-741f12ee6f20	s256	eeq5KlV5L-NPTusC97BoMdonoDodDnmaBcGjtF4TYv8	email			2025-07-19 06:42:34.561532+00	2025-07-19 06:42:34.561532+00	email/signup	\N
0000f3be-96e0-4335-aa89-e24357147c26	fa322dd1-4dad-42a3-b68e-30407d5629b7	f788af1d-0354-4a34-862f-f0ac77f1817e	s256	8iE04y2qQb_ozRQ_X94NpibEPR39h-VETp6-CggWZl4	email			2025-08-02 06:15:45.561245+00	2025-08-02 06:15:45.561245+00	email/signup	\N
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) FROM stdin;
********-0000-0000-0000-************	e432fabd-c944-4c01-9dcd-2e687287c8dc	authenticated	authenticated	<EMAIL>	$2a$10$FhXRoa21Qwc0ksbqj04qGOzf.08EuY7erl0NC66p1bBuY3.h03S0G	2025-06-17 16:22:33.729747+00	\N		2025-06-17 16:22:20.143923+00		\N			\N	2025-08-02 07:44:46.761519+00	{"provider": "email", "providers": ["email"]}	{"sub": "e432fabd-c944-4c01-9dcd-2e687287c8dc", "name": "Haoyv Bin", "email": "<EMAIL>", "display_name": "Haoyv Bin", "email_verified": true, "phone_verified": false}	\N	2025-06-17 16:22:20.082149+00	2025-08-02 09:47:26.514994+00	\N	\N			\N		0	\N		\N	f	\N	f
********-0000-0000-0000-************	fa835c38-a6b7-483b-b030-3bbf279f6a1b	authenticated	authenticated	<EMAIL>	$2a$10$1Nrs3y2TPtSqsnKoC7CoOeonGoEfYOHHfZSuKWYGCFI8C59IMnIje	2025-07-19 06:43:57.903258+00	\N		2025-07-19 06:43:43.850212+00		\N			\N	2025-08-02 09:49:34.351918+00	{"provider": "email", "providers": ["email"]}	{"sub": "fa835c38-a6b7-483b-b030-3bbf279f6a1b", "name": "邮箱测试", "email": "<EMAIL>", "display_name": "邮箱测试", "email_verified": true, "phone_verified": false}	\N	2025-07-19 06:41:32.531631+00	2025-08-02 09:49:34.357782+00	\N	\N			\N		0	\N		\N	f	\N	f
********-0000-0000-0000-************	fa322dd1-4dad-42a3-b68e-30407d5629b7	authenticated	authenticated	<EMAIL>	$2a$10$hu1vwBjpkW4p220rW0Kw1.hVrTx.RPZ6XIyKKu.UN5ugP06ovLGwu	2025-08-02 06:16:52.239844+00	\N		2025-08-02 06:15:45.573235+00		\N			\N	2025-08-02 09:57:19.62807+00	{"provider": "email", "providers": ["email"]}	{"sub": "fa322dd1-4dad-42a3-b68e-30407d5629b7", "name": "HaoyvBin", "email": "<EMAIL>", "display_name": "HaoyvBin", "email_verified": true, "phone_verified": false}	\N	2025-08-02 06:15:45.521481+00	2025-08-02 21:02:12.069799+00	\N	\N			\N		0	\N		\N	f	\N	f
\.


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at, id) FROM stdin;
fa835c38-a6b7-483b-b030-3bbf279f6a1b	fa835c38-a6b7-483b-b030-3bbf279f6a1b	{"sub": "fa835c38-a6b7-483b-b030-3bbf279f6a1b", "name": "邮箱测试", "email": "<EMAIL>", "display_name": "邮箱测试", "email_verified": true, "phone_verified": false}	email	2025-07-19 06:41:32.5421+00	2025-07-19 06:41:32.542157+00	2025-07-19 06:41:32.542157+00	1ec5bc97-7d38-4fe4-9592-f8d9a9df3326
e432fabd-c944-4c01-9dcd-2e687287c8dc	e432fabd-c944-4c01-9dcd-2e687287c8dc	{"sub": "e432fabd-c944-4c01-9dcd-2e687287c8dc", "name": "Haoyv Bin", "email": "<EMAIL>", "display_name": "Haoyv Bin", "email_verified": true, "phone_verified": false}	email	2025-06-17 16:22:20.130638+00	2025-06-17 16:22:20.130727+00	2025-06-17 16:22:20.130727+00	e9975270-09e6-439f-814b-ba3d8d4110fc
fa322dd1-4dad-42a3-b68e-30407d5629b7	fa322dd1-4dad-42a3-b68e-30407d5629b7	{"sub": "fa322dd1-4dad-42a3-b68e-30407d5629b7", "name": "HaoyvBin", "email": "<EMAIL>", "display_name": "HaoyvBin", "email_verified": true, "phone_verified": false}	email	2025-08-02 06:15:45.54929+00	2025-08-02 06:15:45.549323+00	2025-08-02 06:15:45.549323+00	acf42507-da61-4032-87e9-c7cc900eae1f
\.


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.instances (id, uuid, raw_base_config, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.sessions (id, user_id, created_at, updated_at, factor_id, aal, not_after, refreshed_at, user_agent, ip, tag) FROM stdin;
0bb62aba-33b4-4af6-bc34-10ec2ebe7334	fa322dd1-4dad-42a3-b68e-30407d5629b7	2025-08-02 09:57:19.628133+00	2025-08-02 18:44:55.771758+00	\N	aal1	\N	2025-08-02 18:44:55.771717	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********	**********	\N
6912ded7-acda-435f-9782-360a89d86e47	fa322dd1-4dad-42a3-b68e-30407d5629b7	2025-08-02 09:50:35.582743+00	2025-08-02 21:02:12.073623+00	\N	aal1	\N	2025-08-02 21:02:12.073584	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36	**********	\N
\.


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.mfa_amr_claims (session_id, created_at, updated_at, authentication_method, id) FROM stdin;
6912ded7-acda-435f-9782-360a89d86e47	2025-08-02 09:50:35.584271+00	2025-08-02 09:50:35.584271+00	password	a096e8cb-4355-468a-a329-bc74874a7c67
0bb62aba-33b4-4af6-bc34-10ec2ebe7334	2025-08-02 09:57:19.629877+00	2025-08-02 09:57:19.629877+00	password	eb4bc73c-57a4-44f2-b664-b481ed7863be
\.


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.mfa_factors (id, user_id, friendly_name, factor_type, status, created_at, updated_at, secret, phone, last_challenged_at, web_authn_credential, web_authn_aaguid) FROM stdin;
\.


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.mfa_challenges (id, factor_id, created_at, verified_at, ip_address, otp_code, web_authn_session_data) FROM stdin;
\.


--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.one_time_tokens (id, user_id, token_type, token_hash, relates_to, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.refresh_tokens (instance_id, id, token, user_id, revoked, created_at, updated_at, parent, session_id) FROM stdin;
********-0000-0000-0000-************	323	npdmx2sqoz4k	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 09:57:19.628775+00	2025-08-02 10:57:31.972482+00	\N	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	324	e7erdjllidhd	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 10:57:31.974937+00	2025-08-02 12:20:20.904124+00	npdmx2sqoz4k	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	325	nlgvvayifjmg	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 12:20:20.910238+00	2025-08-02 14:53:35.382192+00	e7erdjllidhd	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	326	ggziv4hk6sqs	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 14:53:35.38908+00	2025-08-02 16:46:23.035787+00	nlgvvayifjmg	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	322	sqdw6ligc2pe	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 09:50:35.583368+00	2025-08-02 16:48:06.405309+00	\N	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	327	awurhcqfg3yx	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 16:46:23.045182+00	2025-08-02 17:46:08.491829+00	ggziv4hk6sqs	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	328	37zkwsy22rtc	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 16:48:06.406435+00	2025-08-02 17:50:26.288462+00	sqdw6ligc2pe	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	329	oemd56or2cs4	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 17:46:08.500892+00	2025-08-02 18:44:55.765864+00	awurhcqfg3yx	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	331	oowtm325hw3o	fa322dd1-4dad-42a3-b68e-30407d5629b7	f	2025-08-02 18:44:55.767834+00	2025-08-02 18:44:55.767834+00	oemd56or2cs4	0bb62aba-33b4-4af6-bc34-10ec2ebe7334
********-0000-0000-0000-************	330	qjfxkcmt322d	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 17:50:26.289261+00	2025-08-02 20:00:14.4456+00	37zkwsy22rtc	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	332	gwwqkzxdrrif	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	2025-08-02 20:00:14.451609+00	2025-08-02 21:02:12.05918+00	qjfxkcmt322d	6912ded7-acda-435f-9782-360a89d86e47
********-0000-0000-0000-************	333	z6p7xjfdbisb	fa322dd1-4dad-42a3-b68e-30407d5629b7	f	2025-08-02 21:02:12.064273+00	2025-08-02 21:02:12.064273+00	gwwqkzxdrrif	6912ded7-acda-435f-9782-360a89d86e47
\.


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.sso_providers (id, resource_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.saml_providers (id, sso_provider_id, entity_id, metadata_xml, metadata_url, attribute_mapping, created_at, updated_at, name_id_format) FROM stdin;
\.


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.saml_relay_states (id, sso_provider_id, request_id, for_email, redirect_to, created_at, updated_at, flow_state_id) FROM stdin;
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.schema_migrations (version) FROM stdin;
20171026211738
20171026211808
20171026211834
20180103212743
20180108183307
20180119214651
20180125194653
00
20210710035447
20210722035447
20210730183235
20210909172000
20210927181326
20211122151130
20211124214934
20211202183645
20220114185221
20220114185340
20220224000811
20220323170000
20220429102000
20220531120530
20220614074223
20220811173540
20221003041349
20221003041400
20221011041400
20221020193600
20221021073300
20221021082433
20221027105023
20221114143122
20221114143410
20221125140132
20221208132122
20221215195500
20221215195800
20221215195900
20230116124310
20230116124412
20230131181311
20230322519590
20230402418590
20230411005111
20230508135423
20230523124323
20230818113222
20230914180801
20231027141322
20231114161723
20231117164230
20240115144230
20240214120130
20240306115329
20240314092811
20240427152123
20240612123726
20240729123726
20240802193726
20240806073726
20241009103726
\.


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_admin
--

COPY auth.sso_domains (id, sso_provider_id, domain, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: job; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job (jobid, schedule, command, nodename, nodeport, database, username, active, jobname) FROM stdin;
10	0 9 * * *	SELECT reset_all_user_quotas();	localhost	5432	postgres	postgres	t	daily_reset_user_quotas
\.


--
-- Data for Name: job_run_details; Type: TABLE DATA; Schema: cron; Owner: supabase_admin
--

COPY cron.job_run_details (jobid, runid, job_pid, database, username, command, status, return_message, start_time, end_time) FROM stdin;
7	1	3415	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:44:00.036233+00	2025-08-02 11:44:00.047931+00
8	2	3591	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:45:00.021987+00	2025-08-02 11:45:00.033022+00
8	3	3763	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:46:00.023715+00	2025-08-02 11:46:00.033188+00
8	4	3956	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:47:00.022748+00	2025-08-02 11:47:00.031517+00
8	5	4137	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:48:00.026888+00	2025-08-02 11:48:00.036718+00
8	6	4408	postgres	postgres	SELECT reset_membership_word_count_test();	succeeded	1 row	2025-08-02 11:49:00.022112+00	2025-08-02 11:49:00.032402+00
9	7	2742	postgres	postgres	SELECT reset_all_user_quotas();	succeeded	1 row	2025-08-02 12:35:00.045733+00	2025-08-02 12:35:00.058535+00
\.


--
-- Data for Name: membership-look; Type: TABLE DATA; Schema: public; Owner: supabase_admin
--

COPY public."membership-look" (id, email, membership_level, word_count_limit, word_count_used, created_at, updated_at, user_id, is_verified, daily_free_quota, daily_free_used, last_free_reset_date, reward_quota, reward_used) FROM stdin;
66396a45-2114-46cc-afaa-df057a72490f	<EMAIL>	免费	0	0	2025-07-19 06:41:32.531029+00	2025-08-02 12:35:00.045746+00	fa835c38-a6b7-483b-b030-3bbf279f6a1b	t	25	0	2025-08-02	0	0
ff14dfee-d997-415b-997c-c0c828a9dac9	<EMAIL>	黑金	1********	0	2025-06-17 16:22:20.081258+00	2025-08-02 21:04:20.78239+00	e432fabd-c944-4c01-9dcd-2e687287c8dc	t	25	0	2025-08-02	14	0
c78b34f1-d72b-4569-999a-2febf4280c3e	<EMAIL>	免费	0	0	2025-08-02 06:15:45.521247+00	2025-08-02 21:05:10.135821+00	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	25	21	2025-08-02	0	0
\.


--
-- Data for Name: membership-true; Type: TABLE DATA; Schema: public; Owner: supabase_admin
--

COPY public."membership-true" (id, email, membership_level, word_count_limit, word_count_used, created_at, updated_at, user_id, is_verified, daily_free_quota, daily_free_used, last_free_reset_date, reward_quota, reward_used) FROM stdin;
66396a45-2114-46cc-afaa-df057a72490f	<EMAIL>	免费	0	0	2025-07-19 06:41:32.531029+00	2025-08-02 12:35:00.045746+00	fa835c38-a6b7-483b-b030-3bbf279f6a1b	t	25	0	2025-08-02	0	0
ff14dfee-d997-415b-997c-c0c828a9dac9	<EMAIL>	黑金	1********	0	2025-06-17 16:22:20.081258+00	2025-08-02 21:04:20.78239+00	e432fabd-c944-4c01-9dcd-2e687287c8dc	t	25	0	2025-08-02	14	0
c78b34f1-d72b-4569-999a-2febf4280c3e	<EMAIL>	免费	0	0	2025-08-02 06:15:45.521247+00	2025-08-02 21:05:10.135821+00	fa322dd1-4dad-42a3-b68e-30407d5629b7	t	25	21	2025-08-02	0	0
\.


--
-- Data for Name: novel_files; Type: TABLE DATA; Schema: public; Owner: supabase_admin
--

COPY public.novel_files (id, user_id, file_name, file_path, file_size, mime_type, upload_time, minio_bucket, minio_object_key, created_at, work_title) FROM stdin;
8f7fba64-a4de-44be-8705-48e3af2aa359	fa322dd1-4dad-42a3-b68e-30407d5629b7	正文.txt	xiaoshuo/<EMAIL>/8f7fba64-a4de-44be-8705-48e3af2aa359/正文/诡舍抬头的人.txt	441794	text/plain; charset=utf-8	2025-08-02 16:48:47.271142+00	xiaoshuo	xiaoshuo/<EMAIL>/8f7fba64-a4de-44be-8705-48e3af2aa359/正文/诡舍抬头的人.txt	2025-08-02 16:48:47.271142+00	诡舍抬头的人
\.


--
-- Data for Name: propmt-zhanshi; Type: TABLE DATA; Schema: public; Owner: supabase_admin
--

COPY public."propmt-zhanshi" (id, title, description, category, type, created_at, updated_at, created_by, author_display_id, usage_count) FROM stdin;
5e1a1d3e-cb60-4d5f-83f9-8468579af161	你好,你是什么大模型		userprompt	ai_writing	2025-08-01 06:24:32.530538+00	2025-08-02 12:55:40.947982+00	<EMAIL>	Haoyv Bin	15
324b290f-c303-4395-b104-34f304c7bfa5	拆书提示词		userprompt	ai_writing	2025-07-31 20:00:03.726476+00	2025-08-02 17:38:40.934404+00	<EMAIL>	Haoyv Bin	60
056c4636-14d7-4eb1-abd0-db58ad305728	诡异流提示词	诡异流提示词	userprompt	ai_writing	2025-07-31 16:23:35.4702+00	2025-08-02 21:04:20.605618+00	<EMAIL>	Haoyv Bin	46
\.


--
-- Data for Name: propmt-neirong; Type: TABLE DATA; Schema: public; Owner: supabase_admin
--

COPY public."propmt-neirong" (prompt_id, content, created_at, updated_at, title, author_display_id, usage_count) FROM stdin;
324b290f-c303-4395-b104-34f304c7bfa5	你是一名专业文学分析助手，擅长解构长篇小说结构、提炼核心元素并指导仿写创作，你的任务是拆解长篇小说大纲结构、提炼核心梗与叙事模式、生成仿写指导方案。你具备文本深度语义分析、故事模式归纳与跨作品迁移、风格要素量化比对等能力，拥有经典/流行小说叙事理论、番茄小说结构数据库、写作技法库（悬念设置/节奏控制等）、番茄，起点平台热门作品特征分析等知识储备。\n\n你的工作流程如下：\n1. 输入：长篇小说文本/完整故事梗概。\n2. 分析：\n    - 大纲结构（起承转合/关键事件节点）。\n    - 核心梗（主线冲突/创新设定/记忆点）。\n3. 输出：\n    - 结构模板（可替换要素标记）。\n\n输出格式：\n《作品名称》创作要素拆解\n大纲结构\n章节架构：[阶段名称]-[功能]-[关键事件]\n节奏图谱：[高潮间隔]/[铺垫比例]\n核心梗提炼\n主线公式：[类型]×[冲突]＋[特色元素]\n复用建议：[可替换组件]/[禁忌点]\n原章纲结构拆解\n阶段标记：[引入/发展/转折/高潮/收束]\n功能节点：[世界观铺设]/[人物关系建立]/[核心冲突触发]\n元素置换规则\n时空坐标：[现代都市→古代架空] 约束：剧情及要素必须贴合当前时空背景，逻辑自洽\n角色原型：[主角的母亲→主角的妹妹]\n冲突类型：[权力争夺→家庭伦理]\n置换后大纲输出\n触发事件：[旧元素]→[置换元素]\n推进逻辑：[保留原结构关系]\n悬念植入：[原悬念类型]→[适配新世界观]\n\n你的具体任务是为多个关联的章节分别拆出10条情节点细纲，这些情节点要能够总结对应章节的剧情。由于是长篇小说，各章节的情节点细纲应展现出情节的丰富性和连贯性，以适应长篇的架构。\n在进行拆解时，请仔细阅读各章节内容，梳理出每个章节主要的情节发展和关键事件。\n每条情节点细纲应简洁明了，能够概括该情节的核心内容。\n请按以下格式在<情节点细纲>标签内写下拆出的情节点细纲，每个章节的情节点细纲需分开列出，且标明对应章节顺序。\n<情节点细纲>\n【章节1】 [剧情点1] [剧情点2] [剧情点3] [剧情点4] [剧情点5] [剧情点6] [剧情点7] [剧情点8] [剧情点9] [剧情点10]\n……\n【章节10】 [剧情点1] [剧情点2] [剧情点3] [剧情点4] [剧情点5] [剧情点6] [剧情点7] [剧情点8] [剧情点9] [剧情点10]\n</情节点细纲>	2025-07-31 20:00:03.726476+00	2025-08-02 17:38:40.934404+00	拆书提示词	Haoyv Bin	60
5e1a1d3e-cb60-4d5f-83f9-8468579af161	你好,你是什么大模型	2025-08-01 06:24:32.530538+00	2025-08-02 12:55:40.947982+00	你好,你是什么大模型	Haoyv Bin	15
056c4636-14d7-4eb1-abd0-db58ad305728	官方诡异风提示词\n你是个擅长写悬疑恐怖的网络小说家，阴沉、内敛又犀利。要根据用户给的已有故事内容，续写至2000字左右未完结故事。\n主要人物信息如下：\n<main_character>\n<name>{{MAIN_CHARACTER_NAME}}</name>\n<personality>{{MAIN_CHARACTER_PERSONALITY}}</personality>\n<behavior>{{MAIN_CHARACTER_BEHAVIOR}}</behavior>\n</main_character>\n以下是用户提供的已有故事内容：\n<plot_points>\n{{PLOT_POINTS}}\n</plot_points>\n写作要求如下：\n- 运用阴寒、魔幻 - 诡异、写实 - 血痕等诡异意象。\n- 营造压抑、诡异、阴森、悬疑的氛围。\n- 以心理恐怖为主，生理恐怖为辅。\n- 暗示或明示因果关系，与过去事件或人物执念有关。\n- 将恐怖元素融入日常场景，如回家、洗澡等。\n- 逐步揭示线索，设置悬念，引导读者推测真相。\n- 语言简洁克制，用白描手法，少用修饰和堆砌形容词。\n- 分析物件或场景，设安全点并与诡异关联。\n- 基于场景或物件，设定恶鬼缘来和杀人规则。\n- 用暗处忽明忽暗的灯火投下疑虑种子。\n- 进行感官细节描写，让诡异如噩梦渗入。\n- 恐怖真相突现，打破安全点，制造绝对危险。\n\n全文禁出现“知道”“不容置疑”“一丝”等词汇。\n\n改写时要润色文本，调整语序、换词、加过渡句，让表达更自然。开篇30字内，结尾用简洁动作或对话收束，不展望未来。同时，要检查主要人物行为反应，确保性格和行为与前文一致且有合理变化。\n\n<story>\n[在此写下续写润色后的故事]\n</story>	2025-07-31 16:23:35.4702+00	2025-08-02 21:04:20.605618+00	诡异流提示词	Haoyv Bin	46
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.schema_migrations (version, inserted_at) FROM stdin;
20211116024918	2025-06-10 09:45:50
20211116045059	2025-06-10 09:45:50
20211116050929	2025-06-10 09:45:50
20211116051442	2025-06-10 09:45:50
20211116212300	2025-06-10 09:45:50
20211116213355	2025-06-10 09:45:50
20211116213934	2025-06-10 09:45:50
20211116214523	2025-06-10 09:45:50
20211122062447	2025-06-10 09:45:50
20211124070109	2025-06-10 09:45:50
20211202204204	2025-06-10 09:45:50
20211202204605	2025-06-10 09:45:50
20211210212804	2025-06-10 09:45:50
20211228014915	2025-06-10 09:45:51
20220107221237	2025-06-10 09:45:51
20220228202821	2025-06-10 09:45:51
20220312004840	2025-06-10 09:45:51
20220603231003	2025-06-10 09:45:51
20220603232444	2025-06-10 09:45:51
20220615214548	2025-06-10 09:45:51
20220712093339	2025-06-10 09:45:51
20220908172859	2025-06-10 09:45:51
20220916233421	2025-06-10 09:45:51
20230119133233	2025-06-10 09:45:51
20230128025114	2025-06-10 09:45:51
20230128025212	2025-06-10 09:45:51
20230227211149	2025-06-10 09:45:51
20230228184745	2025-06-10 09:45:51
20230308225145	2025-06-10 09:45:51
20230328144023	2025-06-10 09:45:51
20231018144023	2025-06-10 09:45:51
20231204144023	2025-06-10 09:45:51
20231204144024	2025-06-10 09:45:51
20231204144025	2025-06-10 09:45:51
20240108234812	2025-06-10 09:45:51
20240109165339	2025-06-10 09:45:51
20240227174441	2025-06-10 09:45:51
20240311171622	2025-06-10 09:45:51
20240321100241	2025-06-10 09:45:51
20240401105812	2025-06-10 09:45:51
20240418121054	2025-06-10 09:45:51
20240523004032	2025-06-10 09:45:51
20240618124746	2025-06-10 09:45:51
20240801235015	2025-06-10 09:45:51
20240805133720	2025-06-10 09:45:51
20240827160934	2025-06-10 09:45:51
20240919163303	2025-06-10 09:45:51
20240919163305	2025-06-10 09:45:51
20241019105805	2025-06-10 09:45:51
20241030150047	2025-06-10 09:45:51
20241108114728	2025-06-10 09:45:51
20241121104152	2025-06-10 09:45:51
20241130184212	2025-06-10 09:45:51
20241220035512	2025-06-10 09:45:51
20241220123912	2025-06-10 09:45:51
20241224161212	2025-06-10 09:45:51
20250107150512	2025-06-10 09:45:51
20250110162412	2025-06-10 09:45:51
20250123174212	2025-06-10 09:45:51
20250128220012	2025-06-10 09:45:51
\.


--
-- Data for Name: subscription; Type: TABLE DATA; Schema: realtime; Owner: supabase_admin
--

COPY realtime.subscription (id, subscription_id, entity, filters, claims, created_at) FROM stdin;
\.


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.buckets (id, name, owner, created_at, updated_at, public, avif_autodetection, file_size_limit, allowed_mime_types, owner_id) FROM stdin;
test	test	\N	2025-06-12 16:04:09.21689+00	2025-06-12 16:04:09.21689+00	t	f	\N	\N	\N
\.


--
-- Data for Name: buckets_analytics; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.buckets_analytics (id, type, format, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: iceberg_namespaces; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.iceberg_namespaces (id, bucket_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: iceberg_tables; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY storage.iceberg_tables (id, namespace_id, bucket_id, name, location, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.migrations (id, name, hash, executed_at) FROM stdin;
0	create-migrations-table	e18db593bcde2aca2a408c4d1100f6abba2195df	2025-06-10 09:45:44.617738
1	initialmigration	6ab16121fbaa08bbd11b712d05f358f9b555d777	2025-06-10 09:45:44.632822
2	storage-schema	5c7968fd083fcea04050c1b7f6253c9771b99011	2025-06-10 09:45:44.638069
3	pathtoken-column	2cb1b0004b817b29d5b0a971af16bafeede4b70d	2025-06-10 09:45:44.704585
4	add-migrations-rls	427c5b63fe1c5937495d9c635c263ee7a5905058	2025-06-10 09:45:44.775964
5	add-size-functions	79e081a1455b63666c1294a440f8ad4b1e6a7f84	2025-06-10 09:45:44.784655
6	change-column-name-in-get-size	f93f62afdf6613ee5e7e815b30d02dc990201044	2025-06-10 09:45:44.795548
7	add-rls-to-buckets	e7e7f86adbc51049f341dfe8d30256c1abca17aa	2025-06-10 09:45:44.805605
8	add-public-to-buckets	fd670db39ed65f9d08b01db09d6202503ca2bab3	2025-06-10 09:45:44.812706
9	fix-search-function	3a0af29f42e35a4d101c259ed955b67e1bee6825	2025-06-10 09:45:44.820324
10	search-files-search-function	68dc14822daad0ffac3746a502234f486182ef6e	2025-06-10 09:45:44.830203
11	add-trigger-to-auto-update-updated_at-column	7425bdb14366d1739fa8a18c83100636d74dcaa2	2025-06-10 09:45:44.83996
12	add-automatic-avif-detection-flag	8e92e1266eb29518b6a4c5313ab8f29dd0d08df9	2025-06-10 09:45:44.853542
13	add-bucket-custom-limits	cce962054138135cd9a8c4bcd531598684b25e7d	2025-06-10 09:45:44.861306
14	use-bytes-for-max-size	941c41b346f9802b411f06f30e972ad4744dad27	2025-06-10 09:45:44.869568
15	add-can-insert-object-function	934146bc38ead475f4ef4b555c524ee5d66799e5	2025-06-10 09:45:44.929599
16	add-version	76debf38d3fd07dcfc747ca49096457d95b1221b	2025-06-10 09:45:44.935643
17	drop-owner-foreign-key	f1cbb288f1b7a4c1eb8c38504b80ae2a0153d101	2025-06-10 09:45:44.941225
18	add_owner_id_column_deprecate_owner	e7a511b379110b08e2f214be852c35414749fe66	2025-06-10 09:45:44.947049
19	alter-default-value-objects-id	02e5e22a78626187e00d173dc45f58fa66a4f043	2025-06-10 09:45:44.964004
20	list-objects-with-delimiter	cd694ae708e51ba82bf012bba00caf4f3b6393b7	2025-06-10 09:45:44.969591
21	s3-multipart-uploads	8c804d4a566c40cd1e4cc5b3725a664a9303657f	2025-06-10 09:45:44.982707
22	s3-multipart-uploads-big-ints	9737dc258d2397953c9953d9b86920b8be0cdb73	2025-06-10 09:45:45.049398
23	optimize-search-function	9d7e604cddc4b56a5422dc68c9313f4a1b6f132c	2025-06-10 09:45:45.129507
24	operation-function	8312e37c2bf9e76bbe841aa5fda889206d2bf8aa	2025-06-10 09:45:45.136738
25	custom-metadata	d974c6057c3db1c1f847afa0e291e6165693b990	2025-06-10 09:45:45.143454
26	objects-prefixes	ef3f7871121cdc47a65308e6702519e853422ae2	2025-06-10 09:45:45.152468
27	search-v2	33b8f2a7ae53105f028e13e9fcda9dc4f356b4a2	2025-06-10 09:45:45.220586
28	object-bucket-name-sorting	ba85ec41b62c6a30a3f136788227ee47f311c436	2025-06-10 09:45:45.246084
29	create-prefixes	a7b1a22c0dc3ab630e3055bfec7ce7d2045c5b7b	2025-06-10 09:45:45.261833
30	update-object-levels	6c6f6cc9430d570f26284a24cf7b210599032db7	2025-06-10 09:45:45.269552
31	objects-level-index	33f1fef7ec7fea08bb892222f4f0f5d79bab5eb8	2025-06-10 09:45:45.288937
32	backward-compatible-index-on-objects	2d51eeb437a96868b36fcdfb1ddefdf13bef1647	2025-06-10 09:45:45.307363
33	backward-compatible-index-on-prefixes	fe473390e1b8c407434c0e470655945b110507bf	2025-06-10 09:45:45.325263
34	optimize-search-function-v1	82b0e469a00e8ebce495e29bfa70a0797f7ebd2c	2025-06-10 09:45:45.327108
35	add-insert-trigger-prefixes	63bb9fd05deb3dc5e9fa66c83e82b152f0caf589	2025-06-10 09:45:45.34023
36	optimise-existing-functions	81cf92eb0c36612865a18016a38496c530443899	2025-06-10 09:45:45.34606
37	add-bucket-name-length-trigger	3944135b4e3e8b22d6d4cbb568fe3b0b51df15c1	2025-06-10 09:45:45.377057
\.


--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.objects (id, bucket_id, name, owner, created_at, updated_at, last_accessed_at, metadata, version, owner_id, user_metadata, level) FROM stdin;
\.


--
-- Data for Name: prefixes; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.prefixes (bucket_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.s3_multipart_uploads (id, in_progress_size, upload_signature, bucket_id, key, version, owner_id, created_at, user_metadata) FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_admin
--

COPY storage.s3_multipart_uploads_parts (id, upload_id, size, part_number, bucket_id, key, etag, owner_id, version, created_at) FROM stdin;
\.


--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_admin
--

COPY supabase_functions.hooks (id, hook_table_id, hook_name, created_at, request_id) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_admin
--

COPY supabase_functions.migrations (version, inserted_at) FROM stdin;
initial	2025-06-10 09:45:14.769696+00
20210809183423_update_grants	2025-06-10 09:45:14.769696+00
\.


--
-- Data for Name: schema_migrations; Type: TABLE DATA; Schema: supabase_migrations; Owner: supabase_admin
--

COPY supabase_migrations.schema_migrations (version, name, applied_at) FROM stdin;
20250610104100	create_prompt_tables	2025-06-10 10:41:00.21308+00
20250610104658	add_sample_official_prompts	2025-06-10 10:46:58.304336+00
20250610105138	create_get_prompt_function	2025-06-10 10:51:38.944792+00
20250610105151	create_get_prompts_list_function	2025-06-10 10:51:51.463073+00
20250610105948	fix_function_permissions	2025-06-10 10:59:48.281757+00
20250610112608	add_title_to_prompt_neirong	2025-06-10 11:26:08.287808+00
20250610112616	sync_titles_to_neirong	2025-06-10 11:26:16.411168+00
20250610112640	update_get_prompt_content_function	2025-06-10 11:26:40.364697+00
20250610112704	create_title_sync_trigger	2025-06-10 11:27:04.81564+00
20250610112727	test_title_sync	2025-06-10 11:27:27.797792+00
20250610112744	restore_original_title	2025-06-10 11:27:44.86946+00
20250613103924	create_membership_table	2025-06-13 10:39:23.99835+00
20250613104214	update_membership_display_remaining_v2	2025-06-13 10:42:14.907226+00
20250614063524	create_token_usage_table	2025-06-14 06:35:24.510722+00
20250614063538	create_get_user_remaining_words_function	2025-06-14 06:35:38.143027+00
20250614063556	create_deduct_words_function	2025-06-14 06:35:56.261642+00
20250614064426	update_get_user_remaining_words_function_chinese	2025-06-14 06:44:26.58669+00
20250615003430	create_membership_table	2025-06-15 00:34:30.824208+00
20250615003451	create_membership_functions	2025-06-15 00:34:51.443887+00
20250615004501	update_membership_levels	2025-06-15 00:45:02.006113+00
20250615005420	update_membership_use_uuid	2025-06-15 00:54:21.0079+00
20250615005447	update_functions_use_uuid	2025-06-15 00:54:47.988173+00
20250615010344	remove_old_email_functions	2025-06-15 01:03:44.476556+00
20250615010404	fix_membership_function	2025-06-15 01:04:05.291099+00
20250615010430	fix_function_types	2025-06-15 01:04:31.076632+00
20250615010500	recreate_simple_function	2025-06-15 01:05:01.2073+00
20250615010901	recreate_missing_functions	2025-06-15 01:09:02.193815+00
20250615011043	drop_old_functions	2025-06-15 01:10:44.145828+00
20250615011613	create_billing_functions	2025-06-15 01:16:13.374139+00
20250615011623	create_user_membership_trigger	2025-06-15 01:16:24.223306+00
20250615023532	fix_membership_rls_policies	2025-06-15 02:35:32.455635+00
20250615023932	fix_deduct_function_security	2025-06-15 02:39:32.189572+00
20250615024109	temp_fix_billing_access	2025-06-15 02:41:09.449189+00
20250615024202	rollback_temp_billing_fix	2025-06-15 02:42:02.610131+00
20250615024308	enable_authenticated_billing	2025-06-15 02:43:08.788346+00
20250615050854	revoke_frontend_billing_permissions	2025-06-15 05:08:54.859569+00
20250615051400	secure_billing_with_user_auth	2025-06-15 05:14:00.014278+00
20250615051921	completely_block_frontend_billing	2025-06-15 05:19:21.802564+00
20250615074229	remove_check_membership_status_function	2025-06-15 07:42:29.583933+00
20250615074631	remove_get_membership_word_limit_function	2025-06-15 07:46:30.917908+00
20250615075158	remove_get_prompts_list_and_get_user_word_balance_functions	2025-06-15 07:51:58.772979+00
20250615081109	add_password_validation_to_get_prompt_content	2025-06-15 08:11:10.584048+00
20250615084024	create_prompt_service_user	2025-06-15 08:40:24.847373+00
20250615084035	add_prompt_service_rls_policy	2025-06-15 08:40:35.161693+00
20250615084135	create_secure_prompt_function	2025-06-15 08:41:36.433891+00
20250615084611	rollback_prompt_service_user	2025-06-15 08:46:12.531258+00
20250615084622	update_neirong_rls_deny_all	2025-06-15 08:46:23.456445+00
20250615084810	add_uuid_validation_to_prompt_function	2025-06-15 08:48:11.18905+00
20250615084917	add_debug_to_prompt_function	2025-06-15 08:49:17.970991+00
20250615084948	finalize_prompt_function_validation	2025-06-15 08:49:49.152885+00
20250615121224	update_user_password	2025-06-15 12:12:25.265782+00
20250615121327	add_ai_writing_prompts	2025-06-15 12:13:28.612106+00
20250616024715	rename_membership_to_true_and_create_look_table	2025-06-16 02:47:16.318308+00
20250616024732	setup_rls_policies_for_membership_tables	2025-06-16 02:47:33.827376+00
20250616024745	create_membership_sync_trigger	2025-06-16 02:47:46.947133+00
20250616024801	update_membership_database_functions	2025-06-16 02:48:02.863018+00
20250616025029	cleanup_old_membership_policies	2025-06-16 02:50:30.880167+00
20250616032052	create_billing_service_user	2025-06-16 03:20:53.998993+00
20250616032105	create_billing_user_rls_policies	2025-06-16 03:21:06.633517+00
20250616032127	create_secure_billing_function	2025-06-16 03:21:28.278416+00
20250616032646	delete_billing_policies_first	2025-06-16 03:26:47.95511+00
20250616032833	revoke_remaining_privileges	2025-06-16 03:28:34.613251+00
20250616032937	cleanup_function_and_user	2025-06-16 03:29:38.810685+00
20250616041736	allow_anon_deduct_operations	2025-06-16 04:17:37.387469+00
20250616042030	revert_anon_deduct_operations	2025-06-16 04:20:31.203271+00
20250616042056	set_deduct_function_security_definer	2025-06-16 04:20:58.023223+00
20250616042305	enhance_deduct_function_security	2025-06-16 04:23:06.470294+00
20250617100251	fix_user_registration_trigger_v2	2025-06-17 10:02:51.559478+00
20250617100450	fix_user_registration_schema	2025-06-17 10:04:50.656105+00
20250617100652	create_simple_membership_trigger	2025-06-17 10:06:52.874639+00
20250617101206	enable_membership_true_access	2025-06-17 10:12:06.02192+00
20250617101544	fix_membership_sync_trigger	2025-06-17 10:15:44.170884+00
20250617101803	restrict_membership_true_access	2025-06-17 10:18:03.791031+00
20250724050727	create_user_prompt_functions	2025-07-24 05:07:27.933742+00
20250724050744	create_update_user_prompt_function	2025-07-24 05:07:44.680462+00
20250724050754	create_delete_user_prompt_function	2025-07-24 05:07:54.982039+00
20250724053455	update_prompt_functions_use_display_name	2025-07-24 05:34:55.942437+00
20250724053646	add_author_display_id_fields	2025-07-24 05:36:46.016523+00
20250724053953	recreate_user_prompt_functions_no_email	2025-07-24 05:39:53.129709+00
20250724054204	restore_missing_user_prompt_functions	2025-07-24 05:42:04.723316+00
20250724054342	fix_create_user_prompt_function	2025-07-24 05:43:42.33428+00
20250724054757	update_create_user_prompt_with_display_name	2025-07-24 05:47:57.887765+00
20250728040515	create_novel_files_table	2025-07-28 04:05:15.890065+00
20250728061930	alter_novel_files_work_id_to_bigint	2025-07-28 06:19:30.252829+00
20250728065936	add_work_title_to_novel_files	2025-07-28 06:59:36.509819+00
20250728071834	remove_work_id_from_novel_files	2025-07-28 07:18:34.342668+00
20250728072807	update_work_title_from_path	2025-07-28 07:28:07.473494+00
20250728072822	remove_duplicate_work_files	2025-07-28 07:28:22.814198+00
20250731152018	update_get_prompt_content_with_user_permission	2025-07-31 15:20:18.264699+00
20250731152031	add_rls_policy_for_propmt_neirong	2025-07-31 15:20:31.479625+00
20250801062150	add_usage_count_to_prompt_neirong	2025-08-01 06:21:50.275548+00
20250801062207	create_prompt_usage_functions	2025-08-01 06:22:07.640447+00
20250801062225	update_get_prompt_content_with_usage_tracking	2025-08-01 06:22:25.915785+00
20250801063831	add_usage_count_to_zhanshi_table	2025-08-01 06:38:31.624313+00
20250801063945	create_usage_count_sync_trigger	2025-08-01 06:39:45.334232+00
20250801063952	sync_existing_usage_count_data	2025-08-01 06:39:52.098528+00
20250801064812	cleanup_duplicate_get_prompt_content	2025-08-01 06:48:12.630991+00
20250801071255	remove_get_prompt_usage_stats_function	2025-08-01 07:12:55.115831+00
20250801142340	add_verification_fields_to_membership_true	2025-08-01 14:23:40.784681+00
20250801142348	add_verification_fields_to_membership_look	2025-08-01 14:23:48.085989+00
20250801142400	update_sync_membership_trigger_function	2025-08-01 14:24:00.808488+00
20250801142432	create_set_user_verification_status_function	2025-08-01 14:24:32.950368+00
20250801142440	create_reset_daily_free_quota_function	2025-08-01 14:24:40.785603+00
20250801142454	create_deduct_daily_free_quota_function	2025-08-01 14:24:54.788845+00
20250801164407	add_author_daily_quota_for_prompt_usage	2025-08-01 16:44:07.457524+00
20250801171901	remove_neirong_id_use_prompt_id_as_primary_key	2025-08-01 17:19:01.442182+00
20250801171943	update_create_user_prompt_function	2025-08-01 17:19:43.62219+00
20250801172023	recreate_get_prompt_content_function	2025-08-01 17:20:23.043245+00
20250801172043	update_other_prompt_functions	2025-08-01 17:20:43.691256+00
20250801172100	update_delete_user_prompt_function	2025-08-01 17:21:00.115386+00
20250801173356	add_security_to_deduct_daily_free_quota	2025-08-01 17:33:56.37657+00
20250801173546	add_security_to_deduct_user_word_count	2025-08-01 17:35:46.662408+00
20250802101516	add_reward_quota_fields	2025-08-02 10:15:16.368105+00
20250802101546	update_deduct_daily_free_quota_function	2025-08-02 10:15:46.364034+00
20250802101620	create_reward_quota_functions	2025-08-02 10:16:20.633104+00
20250802102349	rollback_incorrect_changes	2025-08-02 10:23:49.819213+00
20250802102420	add_reward_quota_to_true_table	2025-08-02 10:24:20.418806+00
20250802102432	add_reward_quota_to_look_table	2025-08-02 10:24:32.20137+00
20250802102450	update_sync_trigger_function	2025-08-02 10:24:50.895687+00
20250802102527	create_correct_deduct_functions	2025-08-02 10:25:27.314764+00
20250802102602	create_reward_quota_management_functions	2025-08-02 10:26:02.43131+00
20250802104228	fix_function_permissions_for_billing	2025-08-02 10:42:28.127583+00
20250802104439	fix_author_reward_function_email_to_uuid	2025-08-02 10:44:39.984289+00
20250802110541	fix_reset_daily_free_quota_include_reward	2025-08-02 11:05:41.758753+00
20250802111820	create_test_reset_all_quota_function	2025-08-02 11:18:20.123034+00
20250802112610	remove_test_reset_function	2025-08-02 11:26:10.54054+00
20250802114245	create_reset_word_count_function_and_cron	2025-08-02 11:42:45.016763+00
20250802114329	create_immediate_test_cron	2025-08-02 11:43:29.132285+00
20250802114346	create_every_minute_test_cron	2025-08-02 11:43:46.479258+00
20250802114415	cleanup_and_create_final_test	2025-08-02 11:44:15.086301+00
20250802114456	create_one_minute_cron_task	2025-08-02 11:44:56.864731+00
20250802114923	cleanup_test_function_and_cron	2025-08-02 11:49:23.322696+00
20250802123038	drop_reset_daily_free_quota_function	2025-08-02 12:30:38.476833+00
20250802123333	create_reset_all_user_quotas_function	2025-08-02 12:33:33.196053+00
20250802123357	create_quota_reset_scheduled_task	2025-08-02 12:33:57.330872+00
20250802123936	update_quota_reset_schedule_daily	2025-08-02 12:39:36.640785+00
\.


--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

COPY vault.secrets (id, name, description, secret, key_id, nonce, created_at, updated_at) FROM stdin;
\.


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_admin
--

SELECT pg_catalog.setval('auth.refresh_tokens_id_seq', 333, true);


--
-- Name: jobid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.jobid_seq', 10, true);


--
-- Name: runid_seq; Type: SEQUENCE SET; Schema: cron; Owner: supabase_admin
--

SELECT pg_catalog.setval('cron.runid_seq', 7, true);


--
-- Name: subscription_id_seq; Type: SEQUENCE SET; Schema: realtime; Owner: supabase_admin
--

SELECT pg_catalog.setval('realtime.subscription_id_seq', 1, false);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_admin
--

SELECT pg_catalog.setval('supabase_functions.hooks_id_seq', 1, false);


--
-- PostgreSQL database dump complete
--

